# Comprehensive Plan Update API Examples

## Overview
The new comprehensive UpdatePlan API supports updating all plan properties with automatic version management. When a plan is currently in use (has active subscriptions), the system automatically creates a new version to preserve existing subscriptions.

## API Endpoint
```
PUT /api/Plans/{planId}
```

## Complete Request Structure

### Full Update Request Example
```json
{
  "name": "Premium Transport Plan",
  "description": "Enhanced plan for transport companies with advanced features",
  "type": "Premium",
  "userType": "TransportCompany",
  "price": 2999.00,
  "currency": "INR",
  "billingInterval": "Monthly",
  "billingIntervalCount": 1,
  "customBillingDays": null,
  "isActive": true,
  "isPublic": true,
  "trialPeriodDays": 14,
  "setupFee": 500.00,
  "setupFeeCurrency": "INR",
  "limits": {
    "rfqLimit": 100,
    "vehicleLimit": 50,
    "carrierLimit": 25,
    "isUnlimited": false
  },
  "features": [
    {
      "action": "Add",
      "featureId": "550e8400-e29b-41d4-a716-446655440001",
      "name": "Advanced Analytics",
      "key": "advanced_analytics",
      "description": "Detailed analytics and reporting",
      "isEnabled": true
    },
    {
      "action": "Update",
      "featureId": "550e8400-e29b-41d4-a716-446655440002",
      "name": "Updated Feature Name",
      "key": "updated_feature_key",
      "description": "Updated feature description",
      "isEnabled": true
    },
    {
      "action": "Remove",
      "featureId": "550e8400-e29b-41d4-a716-446655440003"
    }
  ]
}
```

## Partial Update Examples

### 1. Update Only Basic Information
```json
{
  "name": "Updated Plan Name",
  "description": "Updated plan description"
}
```

### 2. Update Price and Currency
```json
{
  "price": 1999.00,
  "currency": "INR"
}
```

### 3. Update Plan Type and User Type
```json
{
  "type": "Enterprise",
  "userType": "CarrierCompany"
}
```

### 4. Update Billing Cycle
```json
{
  "billingInterval": "Quarterly",
  "billingIntervalCount": 1
}
```

### 5. Custom Billing Cycle
```json
{
  "billingInterval": "Custom",
  "billingIntervalCount": 1,
  "customBillingDays": 45
}
```

### 6. Update Specific Limits
```json
{
  "limits": {
    "rfqLimit": 200,
    "vehicleLimit": 100,
    "carrierLimit": 50,
    "isUnlimited": false
  }
}
```

### 7. Make Plan Unlimited
```json
{
  "limits": {
    "isUnlimited": true
  }
}
```

### 8. Update Trial and Setup Fee
```json
{
  "trialPeriodDays": 30,
  "setupFee": 1000.00,
  "setupFeeCurrency": "INR"
}
```

### 9. Update Status Flags
```json
{
  "isActive": false,
  "isPublic": true
}
```

### 10. Add Multiple Features
```json
{
  "features": [
    {
      "action": "Add",
      "featureId": "550e8400-e29b-41d4-a716-446655440001",
      "name": "Real-time Tracking",
      "key": "realtime_tracking",
      "description": "Live vehicle tracking",
      "isEnabled": true
    },
    {
      "action": "Add",
      "featureId": "550e8400-e29b-41d4-a716-446655440002",
      "name": "Route Optimization",
      "key": "route_optimization",
      "description": "AI-powered route optimization",
      "isEnabled": true
    }
  ]
}
```

## Response Examples

### Successful Update Response
```json
{
  "success": true,
  "message": "Plan updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Premium Transport Plan",
    "description": "Enhanced plan for transport companies",
    "version": 1,
    "type": "Premium",
    "userType": "TransportCompany",
    "price": 2999.00,
    "currency": "INR",
    "billingInterval": "Monthly",
    "billingIntervalCount": 1,
    "customBillingDays": null,
    "isPublic": true,
    "trialPeriodDays": 14,
    "setupFee": 500.00,
    "setupFeeCurrency": "INR",
    "limits": {
      "rfqLimit": 100,
      "vehicleLimit": 50,
      "carrierLimit": 25,
      "isUnlimited": false
    },
    "features": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440001",
        "featureId": "550e8400-e29b-41d4-a716-446655440001",
        "name": "Advanced Analytics",
        "key": "advanced_analytics",
        "description": "Detailed analytics and reporting",
        "isEnabled": true
      }
    ],
    "createdAt": "2025-01-08T10:00:00Z",
    "updatedAt": "2025-01-08T11:30:00Z"
  },
  "updatedAt": "2025-01-08T11:30:00Z"
}
```

### New Version Created Response
```json
{
  "success": true,
  "message": "Plan updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "Premium Transport Plan",
    "version": 2,
    "type": "Premium",
    "userType": "TransportCompany",
    "price": 3999.00,
    "currency": "INR",
    "isActive": true,
    "createdAt": "2025-01-08T10:00:00Z",
    "updatedAt": "2025-01-08T11:30:00Z"
  },
  "updatedAt": "2025-01-08T11:30:00Z"
}
```

## Error Responses

### Validation Error
```json
{
  "message": "Validation failed",
  "type": "ValidationError",
  "errors": [
    {
      "property": "Price",
      "message": "Price cannot be negative"
    },
    {
      "property": "Currency",
      "message": "Currency is required when price is provided"
    }
  ]
}
```

### Business Rule Violation
```json
{
  "message": "Cannot change currency of existing plan",
  "type": "DomainError"
}
```

### Plan Not Found
```json
{
  "message": "Plan with ID 550e8400-e29b-41d4-a716-446655440000 not found",
  "type": "DomainError"
}
```

## Key Features

### 1. **Automatic Version Management**
- Creates new version if current plan has active subscriptions
- Preserves existing subscriptions on old version
- Maintains logical plan grouping with PlanId

### 2. **Comprehensive Field Support**
- All plan properties can be updated
- Individual unlimited flags for different limit types
- Feature management (add/update/remove)
- Billing cycle flexibility

### 3. **Business Rule Enforcement**
- Currency change prevention
- Business combination uniqueness validation
- Custom billing cycle validation
- Limit consistency checks

### 4. **Flexible Updates**
- All fields are optional
- Partial updates supported
- Batch feature operations
- Audit trail with update reasons

### 5. **Integration Events**
- Publishes `plan.updated` for existing version updates
- Publishes `plan.version.created` for new versions
- Includes change tracking and audit information
