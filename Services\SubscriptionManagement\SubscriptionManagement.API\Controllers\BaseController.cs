using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using SubscriptionManagement.Domain.Exceptions;
using FluentValidation;

namespace SubscriptionManagement.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public abstract class BaseController : ControllerBase
    {
        private IMediator? _mediator;
        protected IMediator Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<IMediator>();

        protected Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                throw new UnauthorizedAccessException("User ID not found in token");
            }
            return userId;
        }

        protected Guid? GetCurrentUserIdOrNull()
        {
            if (!User.Identity?.IsAuthenticated ?? true)
                return null;

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return null;
            }
            return userId;
        }

        protected string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
        }

        protected bool IsAdmin()
        {
            return User.IsInRole("Admin");
        }

        protected IActionResult HandleResult<T>(T result)
        {
            if (result == null)
                return NotFound();

            return Ok(result);
        }

        protected IActionResult HandleException(Exception ex)
        {
            // Log the exception here
            return ex switch
            {
                SubscriptionDomainException domainEx => BadRequest(new {
                    message = domainEx.Message,
                    type = "DomainError"
                }),
                ValidationException validationEx => BadRequest(new {
                    message = "Validation failed",
                    type = "ValidationError",
                    errors = validationEx.Errors.Select(e => new {
                        property = e.PropertyName,
                        message = e.ErrorMessage
                    })
                }),
                UnauthorizedAccessException => Unauthorized(new {
                    message = "Unauthorized access"
                }),
                ArgumentException argEx => BadRequest(new {
                    message = argEx.Message,
                    type = "ArgumentError"
                }),
                _ => StatusCode(500, new {
                    message = "An error occurred while processing your request.",
                    type = "InternalError"
                })
            };
        }
    }
}
