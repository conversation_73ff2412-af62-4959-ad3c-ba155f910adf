using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.CreatePlan;
using SubscriptionManagement.Application.Commands.AddPlanFeature;
using SubscriptionManagement.Application.Commands.CreatePlanVersion;
using SubscriptionManagement.Application.Commands.UpdatePlan;
using SubscriptionManagement.Application.Commands.UpdatePlanStatus;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Queries.GetPublicPlans;
using SubscriptionManagement.Application.Queries.GetPlan;
using SubscriptionManagement.Application.Queries.GetPlansByUserType;
using SubscriptionManagement.Application.Queries.GetAllPlans;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using FluentValidation;

namespace SubscriptionManagement.API.Controllers
{
    public class PlansController : BaseController
    {
        /// <summary>
        /// Get all public plans
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(List<PlanSummaryDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPublicPlans([FromQuery] UserType? userType = null)
        {
            try
            {
                var query = new GetPublicPlansQuery(userType);
                var plans = await Mediator.Send(query);
                return Ok(plans);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get plan by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(PlanDetailDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPlan(Guid id)
        {
            try
            {
                var query = new GetPlanQuery(id);
                var plan = await Mediator.Send(query);

                if (plan == null)
                {
                    return NotFound($"Plan with ID {id} not found");
                }

                return Ok(plan);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get plans by user type
        /// </summary>
        [HttpGet("by-user-type/{userType}")]
        [ProducesResponseType(typeof(List<PlanDetailDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPlansByUserType(UserType userType)
        {
            try
            {
                var query = new GetPlansByUserTypeQuery(userType);
                var plans = await Mediator.Send(query);
                return Ok(plans);
            }
            catch (Exception ex)
            {

                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create a new plan (Admin only)
        /// </summary>
        [HttpPost]
        //[Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreatePlan([FromBody] CreatePlanDto request)
        {
            try
            {
                var command = new CreatePlanCommand
                {
                    Name = request.Name,
                    Description = request.Description,
                    Version = request.Version,
                    Type = request.Type,
                    UserType = request.UserType,
                    Price = request.Price,
                    Currency = request.Currency,
                    BillingInterval = request.BillingInterval,
                    BillingIntervalCount = request.BillingIntervalCount,
                    CustomBillingDays = request.CustomBillingDays,
                    IsPublic = request.IsPublic,
                    TrialPeriodDays = request.TrialPeriodDays,
                    SetupFee = request.SetupFee,
                    SetupFeeCurrency = request.SetupFeeCurrency,
                    Limits = request.Limits,
                    Features = request.Features
                };

                var planId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetPlan), new { id = planId }, planId);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message, details = ex.ToString() });
            }
        }

        /// <summary>
        /// Update plan with comprehensive field support (Admin only)
        /// Automatically handles version management - creates new version if current is in use
        /// </summary>
        [HttpPut("{id}")]
        //[Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(PlanDetailDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePlan(Guid id, [FromBody] UpdatePlanDto request)
        {
            try
            {
                var command = new UpdatePlanCommand(id)
                {
                    Name = request.Name,
                    Description = request.Description,
                    Type = request.Type,
                    UserType = request.UserType,
                    Price = request.Price,
                    Currency = request.Currency,
                    BillingInterval = request.BillingInterval,
                    BillingIntervalCount = request.BillingIntervalCount,
                    CustomBillingDays = request.CustomBillingDays,
                    IsActive = request.IsActive,
                    IsPublic = request.IsPublic,
                    TrialPeriodDays = request.TrialPeriodDays,
                    SetupFee = request.SetupFee,
                    SetupFeeCurrency = request.SetupFeeCurrency,
                    Limits = request.Limits,
                    Features = request.Features,
                    UpdatedBy = GetCurrentUserIdOrNull(),
                    UpdateReason = "Plan updated via API"
                };

                var result = await Mediator.Send(command);

                return Ok(new
                {
                    success = true,
                    message = "Plan updated successfully",
                    data = result,
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Activate/Deactivate plan (Admin only)
        /// </summary>
        [HttpPatch("{id}/status")]
        //[Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePlanStatus(Guid id, [FromBody] bool isActive)
        {
            try
            {
                var command = new UpdatePlanStatusCommand(
                    planId: id,
                    isActive: isActive,
                    reason: null, // Could be enhanced to accept reason from request body
                    updatedBy: GetCurrentUserId() // Helper method to get current user ID
                );

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new
                    {
                        success = true,
                        message = $"Plan {(isActive ? "activated" : "deactivated")} successfully",
                        planId = id,
                        isActive = isActive,
                        updatedAt = DateTime.UtcNow
                    });
                }

                return BadRequest(new { message = "Failed to update plan status" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get all plans with comprehensive filtering options (Admin only)
        /// </summary>
        /// <param name="page">Page number for pagination (default: 1)</param>
        /// <param name="pageSize">Number of items per page (default: 10, max: 100)</param>
        /// <param name="userType">Filter by user type (e.g., TransportCompany, Carrier, Broker)</param>
        /// <param name="isActive">Filter by plan status (true for active, false for inactive)</param>
        /// <param name="searchTerm">Search by Plan Name - partial plan name matching (case-insensitive)</param>
        /// <param name="planType">Filter by plan type (Basic, Standard, Pro, Premium, Enterprise)</param>

        /// <returns>Paginated list of plans with filtering applied</returns>
        [HttpGet("admin/all")]
        //[Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(GetAllPlansResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllPlans(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] UserType? userType = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] Guid? planTypeId = null)
        {
            try
            {
                var query = new GetAllPlansQuery(
                    page,
                    pageSize,
                    userType,
                    isActive,
                    searchTerm,
                    planTypeId);

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get plan feature IDs (for Identity service integration)
        /// </summary>
        [HttpGet("{id}/features")]
        [AllowAnonymous] // Allow Identity service to call this
        [ProducesResponseType(typeof(PlanFeatureIdsDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPlanFeatureIds(Guid id)
        {
            try
            {
                var query = new GetPlanQuery(id);
                var plan = await Mediator.Send(query);

                if (plan == null)
                {
                    return NotFound($"Plan with ID {id} not found");
                }

                var featureIds = plan.Features?.Select(f => f.FeatureId).ToList() ?? new List<Guid>();

                return Ok(new PlanFeatureIdsDto
                {
                    PlanId = id,
                    FeatureIds = featureIds
                });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Add feature to plan (Admin only)
        /// </summary>
        [HttpPost("{id}/features")]
        //[Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> AddPlanFeature(Guid id, [FromBody] CreatePlanFeatureDto request)
        {
            try
            {
                var command = new AddPlanFeatureCommand(
                    id,
                    request.FeatureId,
                    request.Name,
                    request.Key,
                    request.Description);

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Feature added to plan successfully" });
                }

                return BadRequest(new { message = "Failed to add feature to plan" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update plan feature (Admin only)
        /// </summary>
        [HttpPut("{id}/features/{featureType}")]
        //  [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePlanFeature(Guid id, FeatureType featureType, [FromBody] UpdatePlanFeatureDto request)
        {
            try
            {
                // TODO: Implement UpdatePlanFeatureCommand
                return Ok(new { message = "Update plan feature endpoint - to be implemented", id, featureType, request });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Remove feature from plan (Admin only)
        /// </summary>
        [HttpDelete("{id}/features/{featureType}")]
        // [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RemovePlanFeature(Guid id, FeatureType featureType)
        {
            try
            {
                // TODO: Implement RemovePlanFeatureCommand
                return Ok(new { message = "Remove plan feature endpoint - to be implemented", id, featureType });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create new plan version or get current version for modification (Admin only)
        /// </summary>
        /// <param name="planId">The ID of the plan</param>
        /// <param name="currentVersion">The current version number</param>
        /// <returns>Plan version response with modification instructions</returns>
        [HttpPost("{planId}/versions")]
        //[Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(PlanVersionResponseDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreatePlanVersion(Guid planId, [FromBody] CreatePlanVersionRequest request)
        {
            try
            {
                var command = new CreatePlanVersionCommand(planId, request.CurrentVersion);
                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }

    /// <summary>
    /// Request model for creating plan version
    /// </summary>
    public class CreatePlanVersionRequest
    {
        /// <summary>
        /// The current version number of the plan
        /// </summary>
        public int CurrentVersion { get; set; }
    }
}
