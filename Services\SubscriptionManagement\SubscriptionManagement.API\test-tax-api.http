### Test SetGlobalTaxConfiguration API (No Auth Required)

POST http://localhost:5003/api/admin/tax/global-configurations
Content-Type: application/json

{
  "taxType": 1,
  "rate": 18.0,
  "isIncluded": false,
  "applicableRegions": [
    "IND"
  ],
  "effectiveDate": "2025-08-08T07:25:40.879Z",
  "expirationDate": "2025-10-08T07:25:40.879Z",
  "priority": 1,
  "description": "Test GST Configuration"
}

### Test with different tax type (TDS)

POST http://localhost:5003/api/admin/tax/global-configurations
Content-Type: application/json

{
  "taxType": 5,
  "rate": 2.0,
  "isIncluded": false,
  "applicableRegions": [
    "IND"
  ],
  "effectiveDate": "2025-08-08T07:25:40.879Z",
  "expirationDate": "2025-10-08T07:25:40.879Z",
  "priority": 2,
  "description": "Test TDS Configuration"
}

### Test with invalid rate (should fail)

POST http://localhost:5003/api/admin/tax/global-configurations
Content-Type: application/json

{
  "taxType": 1,
  "rate": 150.0,
  "isIncluded": false,
  "applicableRegions": [
    "IND"
  ],
  "effectiveDate": "2025-08-08T07:25:40.879Z",
  "expirationDate": "2025-10-08T07:25:40.879Z",
  "priority": 1,
  "description": "Invalid rate test"
}

### Test with empty regions (should fail)

POST http://localhost:5003/api/admin/tax/global-configurations
Content-Type: application/json

{
  "taxType": 1,
  "rate": 18.0,
  "isIncluded": false,
  "applicableRegions": [],
  "effectiveDate": "2025-08-08T07:25:40.879Z",
  "expirationDate": "2025-10-08T07:25:40.879Z",
  "priority": 1,
  "description": "Empty regions test"
}

### Get Global Tax Configurations

GET http://localhost:5003/api/admin/tax/global-configurations
