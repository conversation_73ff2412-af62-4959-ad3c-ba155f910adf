using MediatR;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.CreatePlan
{
    public class CreatePlanCommand : IRequest<Guid>
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Version { get; set; } = 1;
        public Guid Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = "INR";
        public BillingInterval BillingInterval { get; set; }
        public int BillingIntervalCount { get; set; } = 1;
        public int? CustomBillingDays { get; set; }
        public bool IsPublic { get; set; } = true;
        public int? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public CreatePlanLimitsDto Limits { get; set; } = new();
        public List<CreatePlanFeatureDto> Features { get; set; } = new();
    }
}
