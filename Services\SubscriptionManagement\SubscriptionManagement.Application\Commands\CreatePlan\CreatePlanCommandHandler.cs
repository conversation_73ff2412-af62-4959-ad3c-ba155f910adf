using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CreatePlan
{
    public class CreatePlanCommandHandler : IRequestHandler<CreatePlanCommand, Guid>
    {
        private readonly IPlanRepository _planRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreatePlanCommandHandler> _logger;

        public CreatePlanCommandHandler(
            IPlanRepository planRepository,
            IMessageBroker messageBroker,
            ILogger<CreatePlanCommandHandler> logger)
        {
            _planRepository = planRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreatePlanCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating plan {PlanName} for user type {UserType}",
                request.Name, request.UserType);

            // Create billing cycle first for validation
            var customDays = request.BillingInterval == BillingInterval.Custom ? request.CustomBillingDays : null;
            var billingCycle = new BillingCycle(request.BillingInterval, request.BillingIntervalCount, customDays);

            // Check if a plan with the same business combination already exists
            var existingBusinessCombination = await _planRepository.GetByBusinessCombinationAsync(
                request.UserType, request.Type, billingCycle);

            if (existingBusinessCombination != null)
            {
                // Log detailed information for debugging
                _logger.LogWarning("Plan creation failed due to duplicate business combination. " +
                    "UserType: {UserType}, PlanType: {PlanType}, BillingCycle: {BillingCycle}. " +
                    "Existing plan: '{ExistingPlanName}' (ID: {ExistingPlanId})",
                    request.UserType, request.Type, billingCycle.GetDisplayName(),
                    existingBusinessCombination.Name, existingBusinessCombination.Id);

                // Generic user-friendly error message
                throw new SubscriptionDomainException(
                    "A plan with this configuration already exists. Please modify the user type, plan type, or billing cycle to create a unique plan.");
            }

            // Create plan limits
            var limits = new PlanLimits(
                request.Limits.RfqLimit,
                request.Limits.VehicleLimit,
                request.Limits.CarrierLimit,
                request.Limits.IsUnlimited);

            // Create money objects
            var price = Money.Create(request.Price, request.Currency);
            Money? setupFee = null;
            if (request.SetupFee.HasValue)
            {
                setupFee = Money.Create(request.SetupFee.Value, request.SetupFeeCurrency ?? request.Currency);
            }

            // Create the plan
            var plan = new Plan(
                request.Name,
                request.Description,
                request.Version,
                request.Type,
                request.UserType,
                price,
                billingCycle,
                limits,
                request.IsPublic,
                request.TrialPeriodDays,
                setupFee);

            // Add features
            foreach (var featureDto in request.Features)
            {
                plan.AddFeature(
                    featureDto.FeatureId,
                    featureDto.Name,
                    featureDto.Key,
                    featureDto.Description,
                    featureDto.IsEnabled);
            }

            // Save the plan with error handling
            try
            {
                await _planRepository.AddAsync(plan);
            }
            catch (DbUpdateException ex) when (ex.InnerException?.Message?.Contains("duplicate key") == true)
            {
                // Handle database constraint violations
                if (ex.InnerException.Message.Contains("IX_Plans_Name"))
                {
                    throw new SubscriptionDomainException($"A plan with the name '{request.Name}' already exists.");
                }
                else if (ex.InnerException.Message.Contains("IX_Plans_BusinessCombination"))
                {
                    throw new SubscriptionDomainException(
                        "A plan with this configuration already exists. Please modify the user type, plan type, or billing cycle to create a unique plan.");
                }
                else
                {
                    // Re-throw the original exception if it's not a known constraint violation
                    throw;
                }
            }

            // Publish integration event
            await _messageBroker.PublishAsync("plan.created", new
            {
                PlanId = plan.Id,
                Name = plan.Name,
                TypeId = plan.TypeId,
                UserType = plan.UserType.ToString(),
                Price = plan.Price.Amount,
                Currency = plan.Price.Currency,
                BillingCycle = plan.BillingCycle.GetDisplayName(),
                IsActive = plan.IsActive,
                IsPublic = plan.IsPublic,
                CreatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Successfully created plan {PlanId} with name {PlanName}",
                plan.Id, request.Name);

            return plan.Id;
        }


    }
}
