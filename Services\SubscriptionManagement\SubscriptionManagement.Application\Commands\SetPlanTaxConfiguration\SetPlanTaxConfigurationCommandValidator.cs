using FluentValidation;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.SetPlanTaxConfiguration
{
    public class SetPlanTaxConfigurationCommandValidator : AbstractValidator<SetPlanTaxConfigurationCommand>
    {
        public SetPlanTaxConfigurationCommandValidator()
        {
            RuleFor(x => x.PlanId)
                .NotEmpty()
                .WithMessage("Plan ID is required");

            RuleFor(x => x.TaxType)
                .IsInEnum()
                .WithMessage("Valid tax type is required");

            RuleFor(x => x.Rate)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Tax rate cannot be negative")
                .LessThanOrEqualTo(100)
                .WithMessage("Tax rate cannot exceed 100%");

            RuleFor(x => x.ApplicableRegions)
                .NotEmpty()
                .WithMessage("At least one applicable region must be specified")
                .Must(regions => regions.All(r => !string.IsNullOrWhiteSpace(r)))
                .WithMessage("All regions must be valid")
                .Must(regions => regions.All(r => r.Length >= 2 && r.Length <= 10))
                .WithMessage("Region codes must be between 2 and 10 characters");

            RuleFor(x => x.EffectiveDate)
                .NotEmpty()
                .WithMessage("Effective date is required")
                .Must(date => date >= DateTime.UtcNow.Date.AddDays(-1))
                .WithMessage("Effective date cannot be more than 1 day in the past");

            RuleFor(x => x.ExpirationDate)
                .GreaterThan(x => x.EffectiveDate)
                .When(x => x.ExpirationDate.HasValue)
                .WithMessage("Expiration date must be after effective date");

            RuleFor(x => x.CreatedByUserId)
                .NotEmpty()
                .WithMessage("Created by user ID is required");

            RuleFor(x => x.Notes)
                .MaximumLength(1000)
                .WithMessage("Notes cannot exceed 1000 characters");

            // Business rule validations
            RuleFor(x => x)
                .Must(ValidateGSTRules)
                .WithMessage("GST rate validation failed")
                .Must(ValidateTDSRules)
                .WithMessage("TDS rate validation failed");
        }

        private bool ValidateGSTRules(SetPlanTaxConfigurationCommand command)
        {
            if (command.TaxType == TaxType.GST &&
                command.ApplicableRegions.Any(IsIndiaRegion))
            {
                return command.Rate <= 28; // GST in India cannot exceed 28%
            }
            return true;
        }

        private static bool IsIndiaRegion(string region)
        {
            return region.Equals("IN", StringComparison.OrdinalIgnoreCase) ||
                   region.Equals("IND", StringComparison.OrdinalIgnoreCase);
        }

        private bool ValidateTDSRules(SetPlanTaxConfigurationCommand command)
        {
            if (command.TaxType == TaxType.TDS)
            {
                return command.Rate <= 30; // TDS cannot exceed 30%
            }
            return true;
        }
    }
}
