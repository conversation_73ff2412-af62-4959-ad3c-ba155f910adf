using FluentValidation;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.TriggerSubscriptionAlerts
{
    public class TriggerSubscriptionAlertsCommandValidator : AbstractValidator<TriggerSubscriptionAlertsCommand>
    {
        public TriggerSubscriptionAlertsCommandValidator()
        {
            RuleFor(x => x.TriggeredByUserId)
                .NotEmpty()
                .WithMessage("TriggeredByUserId is required");

            RuleFor(x => x.AlertType)
                .IsInEnum()
                .WithMessage("Valid alert type is required");

            RuleFor(x => x.Channels)
                .NotEmpty()
                .WithMessage("At least one notification channel must be specified")
                .Must(channels => channels.All(c => IsValidChannel(c)))
                .WithMessage("All channels must be valid (Email, SMS, WhatsApp, InApp)");

            RuleFor(x => x.Language)
                .NotEmpty()
                .WithMessage("Language is required")
                .Length(2, 10)
                .WithMessage("Language must be between 2 and 10 characters");

            RuleFor(x => x.CustomMessage)
                .MaximumLength(2000)
                .WithMessage("Custom message cannot exceed 2000 characters");

            RuleFor(x => x.BatchSize)
                .GreaterThan(0)
                .WithMessage("Batch size must be greater than 0")
                .LessThanOrEqualTo(1000)
                .WithMessage("Batch size cannot exceed 1000");

            RuleFor(x => x.ExpiryDateFrom)
                .LessThanOrEqualTo(x => x.ExpiryDateTo)
                .When(x => x.ExpiryDateFrom.HasValue && x.ExpiryDateTo.HasValue)
                .WithMessage("ExpiryDateFrom cannot be greater than ExpiryDateTo");

            RuleFor(x => x.SubscriptionStatuses)
                .Must(statuses => statuses == null || statuses.All(s => Enum.IsDefined(typeof(SubscriptionStatus), s)))
                .WithMessage("All subscription statuses must be valid");

            RuleFor(x => x.UserTypes)
                .Must(types => types == null || types.All(t => Enum.IsDefined(typeof(UserType), t)))
                .WithMessage("All user types must be valid");

            RuleFor(x => x.PlanTypeIds)
                .Must(types => types == null || types.All(t => t != Guid.Empty))
                .WithMessage("All plan type IDs must be valid (non-empty GUIDs)");

            RuleForEach(x => x.SubscriptionIds)
                .NotEmpty()
                .When(x => x.SubscriptionIds != null)
                .WithMessage("Subscription IDs cannot be empty");
        }

        private bool IsValidChannel(string channel)
        {
            var validChannels = new[] { "Email", "SMS", "WhatsApp", "InApp" };
            return validChannels.Contains(channel, StringComparer.OrdinalIgnoreCase);
        }
    }
}
