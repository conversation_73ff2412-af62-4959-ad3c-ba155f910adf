using MediatR;

namespace SubscriptionManagement.Application.Commands.UpdatePlanStatus
{
    /// <summary>
    /// Command to update the active status of a plan
    /// </summary>
    public class UpdatePlanStatusCommand : IRequest<bool>
    {
        /// <summary>
        /// The ID of the plan to update
        /// </summary>
        public Guid PlanId { get; set; }

        /// <summary>
        /// The new active status for the plan
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Optional reason for the status change (for audit purposes)
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// ID of the user making the change (for audit purposes)
        /// </summary>
        public Guid? UpdatedBy { get; set; }

        public UpdatePlanStatusCommand(Guid planId, bool isActive, string? reason = null, Guid? updatedBy = null)
        {
            PlanId = planId;
            IsActive = isActive;
            Reason = reason;
            UpdatedBy = updatedBy;
        }

        public UpdatePlanStatusCommand() { }
    }
}
