using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Application.Interfaces;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.UpdatePlanStatus
{
    /// <summary>
    /// Handler for updating plan status (activate/deactivate)
    /// </summary>
    public class UpdatePlanStatusCommandHandler : IRequestHandler<UpdatePlanStatusCommand, bool>
    {
        private readonly IPlanRepository _planRepository;
        private readonly ILogger<UpdatePlanStatusCommandHandler> _logger;
        private readonly IMessageBroker _messageBroker;

        public UpdatePlanStatusCommandHandler(
            IPlanRepository planRepository,
            ILogger<UpdatePlanStatusCommandHandler> logger,
            IMessageBroker messageBroker)
        {
            _planRepository = planRepository ?? throw new ArgumentNullException(nameof(planRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _messageBroker = messageBroker ?? throw new ArgumentNullException(nameof(messageBroker));
        }

        public async Task<bool> Handle(UpdatePlanStatusCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating plan status for plan {PlanId} to {IsActive}",
                request.PlanId, request.IsActive);

            try
            {
                // Get the plan
                var plan = await _planRepository.GetByIdAsync(request.PlanId);
                if (plan == null)
                {
                    _logger.LogWarning("Plan not found: {PlanId}", request.PlanId);
                    throw new SubscriptionDomainException($"Plan with ID {request.PlanId} not found");
                }

                // Check if status change is needed
                if (plan.IsActive == request.IsActive)
                {
                    _logger.LogInformation("Plan {PlanId} is already {Status}",
                        request.PlanId, request.IsActive ? "active" : "inactive");
                    return true; // No change needed, but operation is successful
                }

                // Store previous status for logging and events
                var previousStatus = plan.IsActive;

                // Update the plan status using domain methods
                if (request.IsActive)
                {
                    plan.Activate();
                    _logger.LogInformation("Activated plan {PlanId} - {PlanName}", plan.Id, plan.Name);
                }
                else
                {
                    // Business rule: Check if plan has active subscriptions before deactivating
                    var hasActiveSubscriptions = await _planRepository.HasActiveSubscriptionsAsync(plan.Id);
                    if (hasActiveSubscriptions)
                    {
                        _logger.LogWarning("Cannot deactivate plan {PlanId} - has active subscriptions", plan.Id);
                        throw new SubscriptionDomainException(
                            $"Cannot deactivate plan '{plan.Name}' because it has active subscriptions. " +
                            "Please wait for subscriptions to expire or migrate users to another plan first.");
                    }

                    plan.Deactivate();
                    _logger.LogInformation("Deactivated plan {PlanId} - {PlanName}", plan.Id, plan.Name);
                }

                // Save changes
                await _planRepository.UpdateAsync(plan);

                // Publish integration event for other services
                await _messageBroker.PublishAsync("plan.status.updated", new
                {
                    PlanId = plan.Id,
                    PlanName = plan.Name,
                    PreviousStatus = previousStatus,
                    NewStatus = plan.IsActive,
                    UpdatedBy = request.UpdatedBy,
                    UpdatedAt = DateTime.UtcNow,
                    Reason = request.Reason,
                    TypeId = plan.TypeId,
                    UserType = plan.UserType.ToString()
                });

                _logger.LogInformation("Successfully updated plan {PlanId} status from {PreviousStatus} to {NewStatus}",
                    request.PlanId, previousStatus, plan.IsActive);

                return true;
            }
            catch (SubscriptionDomainException)
            {
                // Re-throw domain exceptions as they contain business logic violations
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating plan status for plan {PlanId}", request.PlanId);
                throw new SubscriptionDomainException($"Failed to update plan status: {ex.Message}", ex);
            }
        }
    }
}
