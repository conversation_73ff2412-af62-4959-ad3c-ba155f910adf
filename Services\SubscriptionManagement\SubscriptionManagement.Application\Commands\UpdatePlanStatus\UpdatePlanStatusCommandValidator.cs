using FluentValidation;

namespace SubscriptionManagement.Application.Commands.UpdatePlanStatus
{
    /// <summary>
    /// Validator for UpdatePlanStatusCommand
    /// </summary>
    public class UpdatePlanStatusCommandValidator : AbstractValidator<UpdatePlanStatusCommand>
    {
        public UpdatePlanStatusCommandValidator()
        {
            RuleFor(x => x.PlanId)
                .NotEmpty()
                .WithMessage("Plan ID is required");

            RuleFor(x => x.Reason)
                .MaximumLength(500)
                .WithMessage("Reason cannot exceed 500 characters")
                .When(x => !string.IsNullOrEmpty(x.Reason));
        }
    }
}
