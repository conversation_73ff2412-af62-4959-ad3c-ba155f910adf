using MediatR;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.UpdatePlan
{
    /// <summary>
    /// Command to update a plan with comprehensive field support
    /// Implements version management: creates new version if current is in use
    /// </summary>
    public class UpdatePlanCommand : IRequest<PlanDetailDto>
    {
        /// <summary>
        /// The ID of the plan to update
        /// </summary>
        public Guid PlanId { get; set; }

        /// <summary>
        /// Plan name (optional)
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Plan description (optional)
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Plan type (Basic, Standard, Premium, Enterprise) (optional)
        /// </summary>
        public Guid? Type { get; set; }

        /// <summary>
        /// User type this plan is designed for (optional)
        /// </summary>
        public UserType? UserType { get; set; }

        /// <summary>
        /// Plan price (optional)
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// Currency for the price (optional, but required if Price is provided)
        /// Note: Cannot change currency of existing plan
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Billing interval (Monthly, Quarterly, etc.) (optional)
        /// </summary>
        public BillingInterval? BillingInterval { get; set; }

        /// <summary>
        /// Number of billing intervals (optional)
        /// </summary>
        public int? BillingIntervalCount { get; set; }

        /// <summary>
        /// Custom billing days (required if BillingInterval is Custom) (optional)
        /// </summary>
        public int? CustomBillingDays { get; set; }

        /// <summary>
        /// Whether the plan is active (optional)
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Whether the plan is publicly available (optional)
        /// </summary>
        public bool? IsPublic { get; set; }

        /// <summary>
        /// Trial period in days (optional)
        /// </summary>
        public int? TrialPeriodDays { get; set; }

        /// <summary>
        /// Setup fee amount (optional)
        /// </summary>
        public decimal? SetupFee { get; set; }

        /// <summary>
        /// Setup fee currency (optional, defaults to plan currency)
        /// </summary>
        public string? SetupFeeCurrency { get; set; }

        /// <summary>
        /// Plan limits (optional)
        /// </summary>
        public UpdatePlanLimitsDto? Limits { get; set; }

        /// <summary>
        /// Plan features to add, update, or remove (optional)
        /// </summary>
        public List<UpdatePlanFeatureDto>? Features { get; set; }

        /// <summary>
        /// Reason for the update (for audit purposes) (optional)
        /// </summary>
        public string? UpdateReason { get; set; }

        /// <summary>
        /// ID of the user making the update (for audit purposes) (optional)
        /// </summary>
        public Guid? UpdatedBy { get; set; }

        /// <summary>
        /// Whether to force create a new version even if current version is not in use
        /// </summary>
        public bool ForceNewVersion { get; set; } = false;

        public UpdatePlanCommand() { }

        public UpdatePlanCommand(Guid planId)
        {
            PlanId = planId;
        }
    }
}
