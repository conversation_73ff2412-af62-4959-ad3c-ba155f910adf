using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.UpdatePlan
{
    /// <summary>
    /// Handler for UpdatePlanCommand
    /// Implements comprehensive plan updates with version management
    /// </summary>
    public class UpdatePlanCommandHandler : IRequestHandler<UpdatePlanCommand, PlanDetailDto>
    {
        private readonly IPlanRepository _planRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<UpdatePlanCommandHandler> _logger;
        private readonly IMessageBroker _messageBroker;

        public UpdatePlanCommandHandler(
            IPlanRepository planRepository,
            IMapper mapper,
            ILogger<UpdatePlanCommandHandler> logger,
            IMessageBroker messageBroker)
        {
            _planRepository = planRepository ?? throw new ArgumentNullException(nameof(planRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _messageBroker = messageBroker ?? throw new ArgumentNullException(nameof(messageBroker));
        }

        public async Task<PlanDetailDto> Handle(UpdatePlanCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating plan {PlanId} with comprehensive changes", request.PlanId);

            try
            {
                // Get the current plan
                var currentPlan = await _planRepository.GetByIdAsync(request.PlanId);
                if (currentPlan == null)
                {
                    _logger.LogWarning("Plan not found: {PlanId}", request.PlanId);
                    throw new SubscriptionDomainException($"Plan with ID {request.PlanId} not found");
                }

                // Determine if we need to create a new version
                var needsNewVersion = await ShouldCreateNewVersion(currentPlan, request);

                Plan planToUpdate;
                if (needsNewVersion)
                {
                    _logger.LogInformation("Creating new version for plan {PlanId} - current version {Version} is in use",
                        request.PlanId, currentPlan.Version);
                    planToUpdate = await CreateNewPlanVersion(currentPlan);
                }
                else
                {
                    _logger.LogInformation("Updating existing plan {PlanId} version {Version} - not in use",
                        request.PlanId, currentPlan.Version);
                    planToUpdate = currentPlan;
                }

                // Store original values for change tracking
                var originalValues = CaptureOriginalValues(planToUpdate);

                // Apply updates to the plan
                await ApplyUpdates(planToUpdate, request);

                // Validate business rules after updates
                await ValidateBusinessRules(planToUpdate, request);

                // Save the plan
                if (needsNewVersion)
                {
                    await _planRepository.AddAsync(planToUpdate);
                }
                else
                {
                    await _planRepository.UpdateAsync(planToUpdate);
                }

                // Publish integration events
                await PublishIntegrationEvents(planToUpdate, originalValues, request, needsNewVersion);

                // Map to response DTO
                var result = _mapper.Map<PlanDetailDto>(planToUpdate);

                _logger.LogInformation("Successfully updated plan {PlanId} - {Action} version {Version}",
                    request.PlanId, needsNewVersion ? "Created new" : "Updated existing", planToUpdate.Version);

                return result;
            }
            catch (SubscriptionDomainException)
            {
                // Re-throw domain exceptions as they contain business logic violations
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating plan {PlanId}", request.PlanId);
                throw new SubscriptionDomainException($"Failed to update plan: {ex.Message}", ex);
            }
        }

        private async Task<bool> ShouldCreateNewVersion(Plan currentPlan, UpdatePlanCommand request)
        {
            // Force new version if requested
            if (request.ForceNewVersion)
            {
                return true;
            }

            // Check if current version is in use (has active subscriptions)
            var isVersionInUse = await _planRepository.IsVersionInUseAsync(currentPlan.PlanId, currentPlan.Version);

            // Create new version if current is in use and we're making significant changes
            if (isVersionInUse && HasSignificantChanges(request))
            {
                return true;
            }

            return false;
        }

        private bool HasSignificantChanges(UpdatePlanCommand request)
        {
            // Significant changes that warrant a new version
            return request.Price.HasValue ||
                   request.Type.HasValue ||
                   request.UserType.HasValue ||
                   request.BillingInterval.HasValue ||
                   request.BillingIntervalCount.HasValue ||
                   request.CustomBillingDays.HasValue ||
                   request.Limits != null ||
                   (request.Features != null && request.Features.Any());
        }

        private async Task<Plan> CreateNewPlanVersion(Plan currentPlan)
        {
            // Get the next version number
            var nextVersion = await _planRepository.GetNextVersionNumberAsync(currentPlan.PlanId);

            // Create new version using domain method
            var newPlan = currentPlan.CreateNewVersion(nextVersion);

            return newPlan;
        }

        private Dictionary<string, object?> CaptureOriginalValues(Plan plan)
        {
            return new Dictionary<string, object?>
            {
                ["Name"] = plan.Name,
                ["Description"] = plan.Description,
                ["TypeId"] = plan.TypeId,
                ["UserType"] = plan.UserType,
                ["Price"] = plan.Price,
                ["BillingCycle"] = plan.BillingCycle,
                ["IsActive"] = plan.IsActive,
                ["IsPublic"] = plan.IsPublic,
                ["TrialPeriodDays"] = plan.TrialPeriodDays,
                ["SetupFeeAmount"] = plan.SetupFeeAmount,
                ["SetupFeeCurrency"] = plan.SetupFeeCurrency,
                ["Limits"] = plan.Limits
            };
        }

        private async Task ApplyUpdates(Plan plan, UpdatePlanCommand request)
        {
            // Update basic properties
            if (!string.IsNullOrEmpty(request.Name))
            {
                plan.UpdateName(request.Name);
            }

            if (!string.IsNullOrEmpty(request.Description))
            {
                plan.UpdateDescription(request.Description);
            }

            // Update plan type and user type
            if (request.Type.HasValue)
            {
                plan.UpdateType(request.Type.Value);
            }

            if (request.UserType.HasValue)
            {
                plan.UpdateUserType(request.UserType.Value);
            }

            // Update billing cycle if provided
            if (request.BillingInterval.HasValue)
            {
                var customDays = request.BillingInterval == BillingInterval.Custom ? request.CustomBillingDays : null;
                var intervalCount = request.BillingIntervalCount ?? 1;
                var newBillingCycle = new BillingCycle(request.BillingInterval.Value, intervalCount, customDays);
                plan.UpdateBillingCycle(newBillingCycle);
            }

            // Update price if provided
            if (request.Price.HasValue)
            {
                var currency = request.Currency ?? plan.Price.Currency;
                var newPrice = Money.Create(request.Price.Value, currency);
                plan.UpdatePrice(newPrice);
            }

            // Update trial period
            if (request.TrialPeriodDays.HasValue)
            {
                plan.UpdateTrialPeriod(request.TrialPeriodDays.Value);
            }

            // Update setup fee
            if (request.SetupFee.HasValue)
            {
                var setupFeeCurrency = request.SetupFeeCurrency ?? request.Currency ?? plan.Price.Currency;
                var setupFee = Money.Create(request.SetupFee.Value, setupFeeCurrency);
                plan.UpdateSetupFee(setupFee);
            }

            // Update limits if provided
            if (request.Limits != null)
            {
                var newLimits = CreatePlanLimits(request.Limits);
                plan.UpdateLimits(newLimits);
            }

            // Update status flags
            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    plan.Activate();
                else
                    plan.Deactivate();
            }

            if (request.IsPublic.HasValue)
            {
                if (request.IsPublic.Value)
                    plan.MakePublic();
                else
                    plan.MakePrivate();
            }

            // Update features if provided
            if (request.Features != null)
            {
                await UpdatePlanFeatures(plan, request.Features);
            }
        }

        private PlanLimits CreatePlanLimits(UpdatePlanLimitsDto limitsDto)
        {
            // Handle IsUnlimited flag
            if (limitsDto.IsUnlimited == true)
            {
                return PlanLimits.Unlimited();
            }

            // Create limits with the provided values
            return new PlanLimits(
                rfqLimit: limitsDto.RfqLimit,
                vehicleLimit: limitsDto.VehicleLimit,
                carrierLimit: limitsDto.CarrierLimit,
                isUnlimited: limitsDto.IsUnlimited ?? false
            );
        }

        private async Task UpdatePlanFeatures(Plan plan, List<UpdatePlanFeatureDto> features)
        {
            foreach (var featureDto in features)
            {
                switch (featureDto.Action)
                {
                    case FeatureUpdateAction.Add:
                        if (featureDto.FeatureId.HasValue && !string.IsNullOrEmpty(featureDto.Name) && !string.IsNullOrEmpty(featureDto.Key))
                        {
                            plan.AddFeature(featureDto.FeatureId.Value, featureDto.Name, featureDto.Key,
                                featureDto.Description, featureDto.IsEnabled ?? true);
                        }
                        break;

                    case FeatureUpdateAction.Update:
                        if (featureDto.FeatureId.HasValue && !string.IsNullOrEmpty(featureDto.Name) && !string.IsNullOrEmpty(featureDto.Key))
                        {
                            plan.UpdateFeature(featureDto.FeatureId.Value, featureDto.Name, featureDto.Key, featureDto.Description);
                        }
                        break;

                    case FeatureUpdateAction.Remove:
                        if (featureDto.FeatureId.HasValue)
                        {
                            plan.RemoveFeature(featureDto.FeatureId.Value);
                        }
                        break;
                }
            }
        }

        private async Task ValidateBusinessRules(Plan plan, UpdatePlanCommand request)
        {
            // Validate business combination uniqueness if core properties changed
            if (request.Type.HasValue || request.UserType.HasValue ||
                request.BillingInterval.HasValue || request.BillingIntervalCount.HasValue || request.CustomBillingDays.HasValue)
            {
                // Check if a plan with the same business combination already exists (excluding current plan)
                var existingBusinessCombination = await _planRepository.GetByBusinessCombinationAsync(
                    plan.UserType, plan.TypeId, plan.BillingCycle);

                if (existingBusinessCombination != null && existingBusinessCombination.Id != plan.Id)
                {
                    throw new SubscriptionDomainException(
                        "A plan with this configuration already exists. Please modify the user type, plan type, or billing cycle to create a unique plan.");
                }
            }

            // Validate currency change restriction
            if (request.Price.HasValue && !string.IsNullOrEmpty(request.Currency))
            {
                if (request.Currency != plan.Price.Currency)
                {
                    throw new SubscriptionDomainException("Cannot change currency of existing plan");
                }
            }

            // Validate custom billing days
            if (request.BillingInterval == BillingInterval.Custom && !request.CustomBillingDays.HasValue)
            {
                throw new SubscriptionDomainException("Custom billing days must be specified for custom billing interval");
            }

            if (request.BillingInterval.HasValue && request.BillingInterval != BillingInterval.Custom && request.CustomBillingDays.HasValue)
            {
                throw new SubscriptionDomainException("Custom billing days should only be specified for custom billing interval");
            }
        }

        private async Task PublishIntegrationEvents(Plan plan, Dictionary<string, object?> originalValues,
            UpdatePlanCommand request, bool isNewVersion)
        {
            var eventData = new
            {
                PlanId = plan.Id,
                PlanLogicalId = plan.PlanId,
                Name = plan.Name,
                Version = plan.Version,
                TypeId = plan.TypeId,
                UserType = plan.UserType.ToString(),
                Price = plan.Price.Amount,
                Currency = plan.Price.Currency,
                BillingCycle = plan.BillingCycle.GetDisplayName(),
                IsActive = plan.IsActive,
                IsPublic = plan.IsPublic,
                UpdatedBy = request.UpdatedBy,
                UpdatedAt = DateTime.UtcNow,
                UpdateReason = request.UpdateReason,
                IsNewVersion = isNewVersion,
                OriginalValues = originalValues
            };

            var eventName = isNewVersion ? "plan.version.created" : "plan.updated";
            await _messageBroker.PublishAsync(eventName, eventData);
        }
    }
}
