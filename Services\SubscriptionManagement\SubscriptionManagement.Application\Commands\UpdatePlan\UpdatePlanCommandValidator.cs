using FluentValidation;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.UpdatePlan
{
    /// <summary>
    /// Validator for UpdatePlanCommand
    /// </summary>
    public class UpdatePlanCommandValidator : AbstractValidator<UpdatePlanCommand>
    {
        public UpdatePlanCommandValidator()
        {
            RuleFor(x => x.PlanId)
                .NotEmpty()
                .WithMessage("Plan ID is required");

            RuleFor(x => x.Name)
                .MaximumLength(200)
                .WithMessage("Plan name cannot exceed 200 characters")
                .When(x => !string.IsNullOrEmpty(x.Name));

            RuleFor(x => x.Description)
                .MaximumLength(1000)
                .WithMessage("Plan description cannot exceed 1000 characters")
                .When(x => !string.IsNullOrEmpty(x.Description));

            RuleFor(x => x.Price)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Price cannot be negative")
                .When(x => x.Price.HasValue);

            RuleFor(x => x.Currency)
                .NotEmpty()
                .WithMessage("Currency is required when price is provided")
                .Length(3)
                .WithMessage("Currency must be a 3-letter code (e.g., INR, USD)")
                .When(x => x.Price.HasValue);

            RuleFor(x => x.BillingIntervalCount)
                .GreaterThan(0)
                .WithMessage("Billing interval count must be greater than 0")
                .When(x => x.BillingIntervalCount.HasValue);

            RuleFor(x => x.CustomBillingDays)
                .GreaterThan(0)
                .WithMessage("Custom billing days must be greater than 0")
                .When(x => x.BillingInterval == BillingInterval.Custom);

            RuleFor(x => x.CustomBillingDays)
                .Empty()
                .WithMessage("Custom billing days should only be specified for custom billing interval")
                .When(x => x.BillingInterval.HasValue && x.BillingInterval != BillingInterval.Custom);

            RuleFor(x => x.TrialPeriodDays)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Trial period days cannot be negative")
                .When(x => x.TrialPeriodDays.HasValue);

            RuleFor(x => x.SetupFee)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Setup fee cannot be negative")
                .When(x => x.SetupFee.HasValue);

            RuleFor(x => x.SetupFeeCurrency)
                .Length(3)
                .WithMessage("Setup fee currency must be a 3-letter code (e.g., INR, USD)")
                .When(x => !string.IsNullOrEmpty(x.SetupFeeCurrency));

            RuleFor(x => x.UpdateReason)
                .MaximumLength(500)
                .WithMessage("Update reason cannot exceed 500 characters")
                .When(x => !string.IsNullOrEmpty(x.UpdateReason));

            // Validate limits if provided
            RuleFor(x => x.Limits)
                .SetValidator(new UpdatePlanLimitsDtoValidator()!)
                .When(x => x.Limits != null);

            // Validate features if provided
            RuleForEach(x => x.Features)
                .SetValidator(new UpdatePlanFeatureDtoValidator())
                .When(x => x.Features != null);
        }
    }

    /// <summary>
    /// Validator for UpdatePlanLimitsDto
    /// </summary>
    public class UpdatePlanLimitsDtoValidator : AbstractValidator<UpdatePlanLimitsDto>
    {
        public UpdatePlanLimitsDtoValidator()
        {
            RuleFor(x => x.RfqLimit)
                .GreaterThanOrEqualTo(0)
                .WithMessage("RFQ limit cannot be negative")
                .When(x => x.RfqLimit.HasValue);

            RuleFor(x => x.VehicleLimit)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Vehicle limit cannot be negative")
                .When(x => x.VehicleLimit.HasValue);

            RuleFor(x => x.CarrierLimit)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Carrier limit cannot be negative")
                .When(x => x.CarrierLimit.HasValue);

            // Business rule: If unlimited flag is true, specific limits should not be set
            RuleFor(x => x.RfqLimit)
                .Empty()
                .WithMessage("RFQ limit should not be set when plan is unlimited")
                .When(x => x.IsUnlimited == true);

            RuleFor(x => x.VehicleLimit)
                .Empty()
                .WithMessage("Vehicle limit should not be set when plan is unlimited")
                .When(x => x.IsUnlimited == true);

            RuleFor(x => x.CarrierLimit)
                .Empty()
                .WithMessage("Carrier limit should not be set when plan is unlimited")
                .When(x => x.IsUnlimited == true);
        }
    }

    /// <summary>
    /// Validator for UpdatePlanFeatureDto
    /// </summary>
    public class UpdatePlanFeatureDtoValidator : AbstractValidator<UpdatePlanFeatureDto>
    {
        public UpdatePlanFeatureDtoValidator()
        {
            RuleFor(x => x.FeatureId)
                .NotEmpty()
                .WithMessage("Feature ID is required for update and remove actions")
                .When(x => x.Action == FeatureUpdateAction.Update || x.Action == FeatureUpdateAction.Remove);

            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Feature name is required for add and update actions")
                .MaximumLength(200)
                .WithMessage("Feature name cannot exceed 200 characters")
                .When(x => x.Action == FeatureUpdateAction.Add || x.Action == FeatureUpdateAction.Update);

            RuleFor(x => x.Key)
                .NotEmpty()
                .WithMessage("Feature key is required for add and update actions")
                .MaximumLength(100)
                .WithMessage("Feature key cannot exceed 100 characters")
                .When(x => x.Action == FeatureUpdateAction.Add || x.Action == FeatureUpdateAction.Update);

            RuleFor(x => x.Description)
                .MaximumLength(500)
                .WithMessage("Feature description cannot exceed 500 characters")
                .When(x => !string.IsNullOrEmpty(x.Description));
        }
    }
}
