using AutoMapper;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Mappings;

public class SubscriptionMappingProfile : Profile
{
    public SubscriptionMappingProfile()
    {
        // Plan mappings
        CreateMap<Plan, PlanDto>()
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Price.Currency))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price.Amount))
            .ForMember(dest => dest.BillingCycle, opt => opt.MapFrom(src => FormatBillingCycle(src.BillingCycle)))
            .ForMember(dest => dest.SetupFee, opt => opt.MapFrom(src => src.SetupFeeAmount.HasValue ? src.SetupFeeAmount.Value / 100m : (decimal?)null))
            .ForMember(dest => dest.TrialPeriodDays, opt => opt.MapFrom(src => src.TrialPeriodDays))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => (string?)null)) // TODO: Add CreatedBy to domain entity
            .ForMember(dest => dest.UpdatedBy, opt => opt.MapFrom(src => (string?)null)) // TODO: Add UpdatedBy to domain entity
            .ForMember(dest => dest.Limits, opt => opt.MapFrom(src => src.Limits))
            .ForMember(dest => dest.Features, opt => opt.MapFrom(src => src.Features));

        CreateMap<Plan, PlanDetailDto>()
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Price.Currency))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price.Amount))
            .ForMember(dest => dest.BillingInterval, opt => opt.MapFrom(src => src.BillingCycle.Interval))
            .ForMember(dest => dest.BillingIntervalCount, opt => opt.MapFrom(src => src.BillingCycle.IntervalCount))
            .ForMember(dest => dest.CustomBillingDays, opt => opt.MapFrom(src => src.BillingCycle.CustomDays))
            .ForMember(dest => dest.SetupFee, opt => opt.MapFrom(src => src.SetupFeeAmount.HasValue ? src.SetupFeeAmount.Value / 100m : (decimal?)null))
            .ForMember(dest => dest.TrialPeriodDays, opt => opt.MapFrom(src => src.TrialPeriodDays))
            .ForMember(dest => dest.Limits, opt => opt.MapFrom(src => src.Limits))
            .ForMember(dest => dest.Features, opt => opt.MapFrom(src => src.Features))
            .ForMember(dest => dest.Type, opt => opt.Ignore()); // Will be set manually in query handlers

        CreateMap<Plan, PlanSummaryDto>()
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Price.Currency))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price.Amount))
            .ForMember(dest => dest.BillingCycle, opt => opt.MapFrom(src => FormatBillingCycle(src.BillingCycle)))
            .ForMember(dest => dest.IsPopular, opt => opt.MapFrom(src => false)) // Default to false, can be set based on business logic
            .ForMember(dest => dest.KeyFeatures, opt => opt.MapFrom(src => src.Features.Where(f => f.IsEnabled).Take(3).Select(f => f.Name).ToList()))
            .ForMember(dest => dest.Type, opt => opt.Ignore()); // Will be set manually in query handlers

        CreateMap<Plan, PlanWithUsageDto>()
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Price.Currency))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price.Amount))
            .ForMember(dest => dest.BillingInterval, opt => opt.MapFrom(src => src.BillingCycle.Interval))
            .ForMember(dest => dest.BillingIntervalCount, opt => opt.MapFrom(src => src.BillingCycle.IntervalCount))
            .ForMember(dest => dest.CustomBillingDays, opt => opt.MapFrom(src => src.BillingCycle.CustomDays))
            .ForMember(dest => dest.SetupFee, opt => opt.MapFrom(src => src.SetupFeeAmount.HasValue ? src.SetupFeeAmount.Value / 100m : (decimal?)null))
            .ForMember(dest => dest.SetupFeeCurrency, opt => opt.MapFrom(src => src.SetupFeeCurrency))
            .ForMember(dest => dest.TrialPeriodDays, opt => opt.MapFrom(src => src.TrialPeriodDays))
            .ForMember(dest => dest.Limits, opt => opt.MapFrom(src => src.Limits))
            .ForMember(dest => dest.Features, opt => opt.MapFrom(src => src.Features))
            .ForMember(dest => dest.UsageStatistics, opt => opt.Ignore()) // This will be set manually in the query handler
            .ForMember(dest => dest.Type, opt => opt.Ignore()); // Will be set manually in query handlers

        // PlanFeature mappings
        CreateMap<PlanFeature, PlanFeatureDto>()
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.GetDisplayName()))
            .ForMember(dest => dest.AccessDescription, opt => opt.MapFrom(src => src.GetAccessDescription()));

        CreateMap<PlanFeature, PlanFeatureDetailDto>()
            .ForMember(dest => dest.FeatureName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.IsEnable, opt => opt.MapFrom(src => src.IsEnabled))
            .ForMember(dest => dest.FeatureId, opt => opt.MapFrom(src => src.FeatureId));

        // Subscription mappings
        CreateMap<Subscription, SubscriptionDto>()
            .ForMember(dest => dest.PlanName, opt => opt.MapFrom(src => src.Plan != null ? src.Plan.Name : string.Empty))
            .ForMember(dest => dest.CurrentPrice, opt => opt.MapFrom(src => src.CurrentPrice.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.CurrentPrice.Currency))
            .ForMember(dest => dest.BillingCycle, opt => opt.MapFrom(src => FormatBillingCycle(src.BillingCycle)))
            .ForMember(dest => dest.IsInTrial, opt => opt.MapFrom(src => src.IsInTrial()))
            .ForMember(dest => dest.IsExpired, opt => opt.MapFrom(src => src.IsExpired()))
            .ForMember(dest => dest.DaysUntilRenewal, opt => opt.MapFrom(src => src.GetDaysUntilRenewal()))
            .ForMember(dest => dest.IsInGracePeriod, opt => opt.MapFrom(src => src.IsInGracePeriod()))
            .ForMember(dest => dest.DaysRemainingInGracePeriod, opt => opt.MapFrom(src => src.GetDaysRemainingInGracePeriod()))
            .ForMember(dest => dest.TotalPaid, opt => opt.MapFrom(src => src.GetTotalPaid().Amount));

        // PlanLimits mappings
        CreateMap<PlanLimits, PlanLimitsDto>();
        CreateMap<CreatePlanLimitsDto, PlanLimits>();
        CreateMap<UpdatePlanLimitsDto, PlanLimits>();
    }

    private static string FormatBillingCycle(BillingCycle billingCycle)
    {
        if (billingCycle.IntervalCount == 1)
        {
            return billingCycle.Interval.ToString();
        }
        return $"Every {billingCycle.IntervalCount} {billingCycle.Interval}s";
    }
}
