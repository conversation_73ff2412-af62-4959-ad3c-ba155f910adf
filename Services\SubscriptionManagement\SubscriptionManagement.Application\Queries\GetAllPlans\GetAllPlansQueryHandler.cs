using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Application.Queries.GetAllPlans;

/// <summary>
/// Handler for GetAllPlansQuery with comprehensive filtering capabilities
/// </summary>
public class GetAllPlansQueryHandler : IRequestHandler<GetAllPlansQuery, GetAllPlansResponse>
{
    private readonly IPlanRepository _planRepository;
    private readonly ITierConfigurationSetupRepository _tierConfigRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAllPlansQueryHandler> _logger;

    public GetAllPlansQueryHandler(
        IPlanRepository planRepository,
        ITierConfigurationSetupRepository tierConfigRepository,
        IMapper mapper,
        ILogger<GetAllPlansQueryHandler> logger)
    {
        _planRepository = planRepository;
        _tierConfigRepository = tierConfigRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<GetAllPlansResponse> Handle(GetAllPlansQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting all plans with filters - Page: {Page}, PageSize: {PageSize}, UserType: {UserType}, IsActive: {IsActive}, SearchTerm: {SearchTerm}, PlanTypeId: {PlanTypeId}",
            request.Page, request.PageSize, request.UserType, request.IsActive, request.SearchTerm, request.PlanTypeId);

        // Validate pagination parameters
        if (request.Page < 1)
        {
            throw new SubscriptionDomainException("Page number must be greater than 0");
        }

        if (request.PageSize < 1 || request.PageSize > 100)
        {
            throw new SubscriptionDomainException("Page size must be between 1 and 100");
        }

        // Validate and sanitize search term
        var sanitizedSearchTerm = string.IsNullOrWhiteSpace(request.SearchTerm)
            ? null
            : request.SearchTerm.Trim();

        if (!string.IsNullOrEmpty(sanitizedSearchTerm) && sanitizedSearchTerm.Length > 100)
        {
            throw new SubscriptionDomainException("Search term cannot exceed 100 characters");
        }

        try
        {
            // Get filtered plans from repository
            var (plans, totalCount) = await _planRepository.GetFilteredPlansAsync(
                request.Page,
                request.PageSize,
                request.UserType,
                request.IsActive,
                sanitizedSearchTerm,
                request.PlanTypeId);

            // Map to DTOs with usage statistics
            var planDtos = new List<PlanWithUsageDto>();

            foreach (var plan in plans)
            {
                var planDto = _mapper.Map<PlanWithUsageDto>(plan);

                // Get Type string from TierConfigurationSetup
                var tierConfig = await _tierConfigRepository.GetByIdAsync(plan.TypeId);
                planDto.Type = tierConfig?.Type ?? "Unknown";

                // Get usage statistics for each plan
                var usageStats = new PlanUsageStatisticsDto();

                // Get active subscriber count
                usageStats.TotalActiveSubscribers = await _planRepository.GetActiveSubscriberCountAsync(plan.Id, plan.Version);

                // Get subscribers by user type
                var subscribersByUserType = await _planRepository.GetSubscriberCountByUserTypeAsync(plan.Id, plan.Version);
                usageStats.SubscribersByUserType = subscribersByUserType.ToDictionary(
                    kvp => kvp.Key.ToString(),
                    kvp => kvp.Value);

                // Determine if plan can be modified (no active subscribers)
                usageStats.CanModify = usageStats.TotalActiveSubscribers == 0;

                planDto.UsageStatistics = usageStats;
                planDtos.Add(planDto);
            }

            var response = new GetAllPlansResponse
            {
                Plans = planDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                Filters = new FilterSummary
                {
                    UserType = request.UserType,
                    IsActive = request.IsActive,
                    SearchTerm = sanitizedSearchTerm,
                    PlanTypeId = request.PlanTypeId
                }
            };

            _logger.LogInformation("Successfully retrieved {Count} plans out of {TotalCount} total plans matching filters. Active filters: {ActiveFiltersCount}",
                planDtos.Count, totalCount, response.Filters.ActiveFiltersCount);

            return response;
        }
        catch (Exception ex) when (!(ex is SubscriptionDomainException))
        {
            _logger.LogError(ex, "Error occurred while retrieving filtered plans");
            throw new SubscriptionDomainException("An error occurred while retrieving plans", ex);
        }
    }
}
