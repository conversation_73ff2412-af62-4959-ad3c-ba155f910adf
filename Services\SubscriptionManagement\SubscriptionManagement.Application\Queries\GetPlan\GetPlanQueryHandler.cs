using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPlan;

public class GetPlanQueryHandler : IRequestHandler<GetPlanQuery, PlanWithUsageDto?>
{
    private readonly IPlanRepository _planRepository;
    private readonly ITierConfigurationSetupRepository _tierConfigRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPlanQueryHandler> _logger;

    public GetPlanQueryHandler(
        IPlanRepository planRepository,
        ITierConfigurationSetupRepository tierConfigRepository,
        IMapper mapper,
        ILogger<GetPlanQueryHandler> logger)
    {
        _planRepository = planRepository;
        _tierConfigRepository = tierConfigRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PlanWithUsageDto?> Handle(GetPlanQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting plan with ID: {PlanId}, Version: {Version}", request.PlanId, request.Version);

        // Get plan by ID and version (if specified)
        var plan = request.Version.HasValue
            ? await _planRepository.GetByIdAndVersionAsync(request.PlanId, request.Version.Value)
            : await _planRepository.GetByIdAsync(request.PlanId);

        if (plan == null)
        {
            _logger.LogWarning("Plan with ID {PlanId} and version {Version} not found", request.PlanId, request.Version);
            return null;
        }

        // Map basic plan data
        var result = _mapper.Map<PlanWithUsageDto>(plan);

        // Get Type string from TierConfigurationSetup
        var tierConfig = await _tierConfigRepository.GetByIdAsync(plan.TypeId);
        result.Type = tierConfig?.Type ?? "Unknown";

        // Get usage statistics
        var usageStats = new PlanUsageStatisticsDto();

        // Get active subscriber count
        usageStats.TotalActiveSubscribers = await _planRepository.GetActiveSubscriberCountAsync(plan.Id, plan.Version);

        // Get subscribers by user type
        var subscribersByUserType = await _planRepository.GetSubscriberCountByUserTypeAsync(plan.Id, plan.Version);
        usageStats.SubscribersByUserType = subscribersByUserType.ToDictionary(
            kvp => kvp.Key.ToString(),
            kvp => kvp.Value);

        // Determine if plan can be modified (no active subscribers)
        usageStats.CanModify = usageStats.TotalActiveSubscribers == 0;

        result.UsageStatistics = usageStats;

        _logger.LogInformation("Successfully retrieved plan {PlanName} (ID: {PlanId}, Version: {Version}) with {SubscriberCount} active subscribers",
            plan.Name, plan.Id, plan.Version, usageStats.TotalActiveSubscribers);

        return result;
    }
}
