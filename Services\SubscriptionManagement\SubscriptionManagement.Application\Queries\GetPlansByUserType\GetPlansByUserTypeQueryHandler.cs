using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPlansByUserType;

public class GetPlansByUserTypeQueryHandler : IRequestHandler<GetPlansByUserTypeQuery, List<PlanDetailDto>>
{
    private readonly IPlanRepository _planRepository;
    private readonly ITierConfigurationSetupRepository _tierConfigRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPlansByUserTypeQueryHandler> _logger;

    public GetPlansByUserTypeQueryHandler(
        IPlanRepository planRepository,
        ITierConfigurationSetupRepository tierConfigRepository,
        IMapper mapper,
        ILogger<GetPlansByUserTypeQueryHandler> logger)
    {
        _planRepository = planRepository;
        _tierConfigRepository = tierConfigRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PlanDetailDto>> Handle(GetPlansByUserTypeQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting plans for user type: {UserType}", request.UserType);

        var plans = await _planRepository.GetByUserTypeAsync(request.UserType);

        // Filter to only public and active plans
        var publicPlans = plans.Where(p => p.IsActive && p.IsPublic).ToList();

        var result = _mapper.Map<List<PlanDetailDto>>(publicPlans);

        // Set Type string for each plan
        foreach (var planDto in result)
        {
            var plan = publicPlans.First(p => p.Id == planDto.Id);
            var tierConfig = await _tierConfigRepository.GetByIdAsync(plan.TypeId);
            planDto.Type = tierConfig?.Type ?? "Unknown";
        }

        _logger.LogInformation("Found {Count} plans for user type {UserType}", result.Count, request.UserType);

        return result;
    }
}
