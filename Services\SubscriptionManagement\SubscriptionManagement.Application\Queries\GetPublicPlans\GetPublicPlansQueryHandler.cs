using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPublicPlans;

public class GetPublicPlansQueryHandler : IRequestHandler<GetPublicPlansQuery, List<PlanSummaryDto>>
{
    private readonly IPlanRepository _planRepository;
    private readonly ITierConfigurationSetupRepository _tierConfigRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPublicPlansQueryHandler> _logger;

    public GetPublicPlansQueryHandler(
        IPlanRepository planRepository,
        ITierConfigurationSetupRepository tierConfigRepository,
        IMapper mapper,
        ILogger<GetPublicPlansQueryHandler> logger)
    {
        _planRepository = planRepository;
        _tierConfigRepository = tierConfigRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PlanSummaryDto>> Handle(GetPublicPlansQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting public plans for user type: {UserType}", request.UserType);

        var plans = request.UserType.HasValue
            ? await _planRepository.GetByUserTypeAsync(request.UserType.Value)
            : await _planRepository.GetPublicAsync();

        // Filter to only public and active plans
        var publicPlans = plans.Where(p => p.IsActive && p.IsPublic).ToList();

        var result = _mapper.Map<List<PlanSummaryDto>>(publicPlans);

        // Set Type string for each plan
        foreach (var planDto in result)
        {
            var plan = publicPlans.First(p => p.Id == planDto.Id);
            var tierConfig = await _tierConfigRepository.GetByIdAsync(plan.TypeId);
            planDto.Type = tierConfig?.Type ?? "Unknown";
        }

        _logger.LogInformation("Found {Count} public plans", result.Count);

        return result;
    }
}
