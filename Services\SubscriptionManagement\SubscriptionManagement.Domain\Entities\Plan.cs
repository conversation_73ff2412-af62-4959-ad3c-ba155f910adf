using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class Plan : AggregateRoot
    {
        public Guid PlanId { get; private set; }  // Logical plan identifier (groups all versions)
        public string Name { get; private set; }
        public string Description { get; private set; }
        public int Version { get; private set; }
        public Guid TypeId { get; private set; }
        public UserType UserType { get; private set; }
        public Money Price { get; private set; }
        public BillingCycle BillingCycle { get; private set; }
        public PlanLimits Limits { get; private set; }
        public bool IsActive { get; private set; }
        public bool IsPublic { get; private set; }
        public int? TrialPeriodDays { get; private set; }
        public int? SetupFeeAmount { get; private set; }
        public string? SetupFeeCurrency { get; private set; }

        private readonly List<PlanFeature> _features = new();
        public IReadOnlyCollection<PlanFeature> Features => _features.AsReadOnly();

        // TODO: Temporarily removed due to EF Core configuration conflicts
        // Will be re-implemented using PlanTaxConfigurations navigation property
        // private readonly List<TaxConfiguration> _taxConfigurations = new();
        // public IReadOnlyCollection<TaxConfiguration> TaxConfigurations => _taxConfigurations.AsReadOnly();

        private readonly List<PlanTaxConfiguration> _planTaxConfigurations = new();
        public IReadOnlyCollection<PlanTaxConfiguration> PlanTaxConfigurations => _planTaxConfigurations.AsReadOnly();

        public Guid? TaxCategoryId { get; private set; }
        public TaxCategory? TaxCategory { get; private set; }

        private Plan() { }

        public Plan(string name, string description, int version, Guid typeId, UserType userType,
            Money price, BillingCycle billingCycle, PlanLimits limits,
            bool isPublic = true, int? trialPeriodDays = null, Money? setupFee = null, Guid? planId = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new SubscriptionDomainException("Plan name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new SubscriptionDomainException("Plan description cannot be empty");

            if (version <= 0)
                throw new SubscriptionDomainException("Plan version must be greater than 0");

            if (price == null)
                throw new SubscriptionDomainException("Plan price cannot be null");

            if (billingCycle == null)
                throw new SubscriptionDomainException("Billing cycle cannot be null");

            if (limits == null)
                throw new SubscriptionDomainException("Plan limits cannot be null");

            if (trialPeriodDays.HasValue && trialPeriodDays.Value < 0)
                throw new SubscriptionDomainException("Trial period days cannot be negative");

            // Set PlanId - use provided value or generate new one for first version
            PlanId = planId ?? Guid.NewGuid();

            Name = name;
            Description = description;
            Version = version;
            TypeId = typeId;
            UserType = userType;
            Price = price;
            BillingCycle = billingCycle;
            Limits = limits;
            IsActive = true;
            IsPublic = isPublic;
            TrialPeriodDays = trialPeriodDays;

            if (setupFee != null)
            {
                SetupFeeAmount = (int)(setupFee.Amount * 100); // Store in cents
                SetupFeeCurrency = setupFee.Currency;
            }

            AddDomainEvent(new PlanCreatedEvent(Id, name, typeId, userType, price.Amount, price.Currency));
        }

        public void AddFeature(Guid featureId, string name, string key, string? description = null, bool isEnabled = true)
        {
            if (_features.Any(f => f.FeatureId == featureId))
                throw new SubscriptionDomainException($"Feature with ID {featureId} already exists in this plan");

            if (_features.Any(f => f.Key == key))
                throw new SubscriptionDomainException($"Feature with key {key} already exists in this plan");

            var feature = new PlanFeature(featureId, name, key, description, isEnabled);
            _features.Add(feature);
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemoveFeature(Guid featureId)
        {
            var feature = _features.FirstOrDefault(f => f.FeatureId == featureId);
            if (feature == null)
                throw new SubscriptionDomainException($"Feature with ID {featureId} does not exist in this plan");

            _features.Remove(feature);
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemoveFeatureByKey(string key)
        {
            var feature = _features.FirstOrDefault(f => f.Key == key);
            if (feature == null)
                throw new SubscriptionDomainException($"Feature with key {key} does not exist in this plan");

            _features.Remove(feature);
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateFeature(Guid featureId, string name, string key, string? description = null)
        {
            var feature = _features.FirstOrDefault(f => f.FeatureId == featureId);
            if (feature == null)
                throw new SubscriptionDomainException($"Feature with ID {featureId} does not exist in this plan");

            // Check if key is being changed to one that already exists
            if (feature.Key != key && _features.Any(f => f.Key == key))
                throw new SubscriptionDomainException($"Feature with key {key} already exists in this plan");

            feature.Update(name, key, description);
            UpdatedAt = DateTime.UtcNow;
        }

        public bool HasFeature(Guid featureId)
        {
            return _features.Any(f => f.FeatureId == featureId);
        }

        public bool HasFeatureByKey(string key)
        {
            return _features.Any(f => f.Key == key);
        }

        public PlanFeature? GetFeature(Guid featureId)
        {
            return _features.FirstOrDefault(f => f.FeatureId == featureId);
        }

        public PlanFeature? GetFeatureByKey(string key)
        {
            return _features.FirstOrDefault(f => f.Key == key);
        }

        public void UpdatePrice(Money newPrice)
        {
            if (newPrice == null)
                throw new SubscriptionDomainException("Price cannot be null");

            if (newPrice.Currency != Price.Currency)
                throw new SubscriptionDomainException("Cannot change currency of existing plan");

            Price = newPrice;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateLimits(PlanLimits newLimits)
        {
            if (newLimits == null)
                throw new SubscriptionDomainException("Limits cannot be null");

            Limits = newLimits;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateName(string newName)
        {
            if (string.IsNullOrWhiteSpace(newName))
                throw new SubscriptionDomainException("Plan name cannot be empty");

            Name = newName;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateDescription(string newDescription)
        {
            if (string.IsNullOrWhiteSpace(newDescription))
                throw new SubscriptionDomainException("Plan description cannot be empty");

            Description = newDescription;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateType(Guid newTypeId)
        {
            TypeId = newTypeId;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateUserType(UserType newUserType)
        {
            UserType = newUserType;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateBillingCycle(BillingCycle newBillingCycle)
        {
            if (newBillingCycle == null)
                throw new SubscriptionDomainException("Billing cycle cannot be null");

            BillingCycle = newBillingCycle;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateTrialPeriod(int? trialPeriodDays)
        {
            if (trialPeriodDays.HasValue && trialPeriodDays.Value < 0)
                throw new SubscriptionDomainException("Trial period days cannot be negative");

            TrialPeriodDays = trialPeriodDays;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateSetupFee(Money? setupFee)
        {
            if (setupFee != null)
            {
                SetupFeeAmount = (int)(setupFee.Amount * 100); // Store in cents
                SetupFeeCurrency = setupFee.Currency;
            }
            else
            {
                SetupFeeAmount = null;
                SetupFeeCurrency = null;
            }
            UpdatedAt = DateTime.UtcNow;
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void MakePublic()
        {
            IsPublic = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void MakePrivate()
        {
            IsPublic = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public Money CalculateProrationAmount(DateTime fromDate, DateTime toDate, Money currentPlanPrice)
        {
            if (fromDate >= toDate)
                throw new SubscriptionDomainException("From date must be before to date");

            var totalDaysInCycle = BillingCycle.GetDaysInCycle();
            var remainingDays = (toDate - fromDate).Days;
            var prorationFactor = (decimal)remainingDays / totalDaysInCycle;

            var currentPlanProration = currentPlanPrice.Multiply(prorationFactor);
            var newPlanProration = Price.Multiply(prorationFactor);

            return newPlanProration.Subtract(currentPlanProration);
        }

        public bool IsCompatibleWith(UserType userType)
        {
            return UserType == userType;
        }

        public bool IsUpgradeFrom(Plan otherPlan)
        {
            if (UserType != otherPlan.UserType)
                return false;

            // Note: Type comparison logic will need to be implemented based on TierConfigurationSetup hierarchy
            // For now, we'll compare TypeId directly (this may need business logic adjustment)
            return TypeId != otherPlan.TypeId; // Placeholder logic
        }

        public bool IsDowngradeFrom(Plan otherPlan)
        {
            if (UserType != otherPlan.UserType)
                return false;

            // Note: Type comparison logic will need to be implemented based on TierConfigurationSetup hierarchy
            // For now, we'll compare TypeId directly (this may need business logic adjustment)
            return TypeId != otherPlan.TypeId; // Placeholder logic
        }

        // Tax-related methods
        public void SetTaxCategory(TaxCategory taxCategory)
        {
            if (taxCategory == null)
                throw new SubscriptionDomainException("Tax category cannot be null");

            TaxCategoryId = taxCategory.Id;
            TaxCategory = taxCategory;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxCategoryChangedEvent(Id, Name, taxCategory.Id, taxCategory.Name));
        }

        public void RemoveTaxCategory()
        {
            if (TaxCategoryId.HasValue)
            {
                var oldCategoryId = TaxCategoryId.Value;
                var oldCategoryName = TaxCategory?.Name ?? "Unknown";

                TaxCategoryId = null;
                TaxCategory = null;
                UpdatedAt = DateTime.UtcNow;

                AddDomainEvent(new PlanTaxCategoryRemovedEvent(Id, Name, oldCategoryId, oldCategoryName));
            }
        }

        public void AddTaxConfiguration(TaxConfiguration taxConfiguration)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            // TODO: Implement using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues
            throw new NotImplementedException("Tax configuration management temporarily disabled");
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxConfigurationAddedEvent(Id, Name, taxConfiguration.TaxType,
                taxConfiguration.Rate, taxConfiguration.ApplicableRegions));
        }

        public void RemoveTaxConfiguration(TaxConfiguration taxConfiguration)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            // TODO: Implement using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues
            throw new NotImplementedException("Tax configuration management temporarily disabled");
        }

        public void UpdateTaxConfiguration(TaxType taxType, List<string> regions, TaxConfiguration newConfiguration)
        {
            // TODO: Implement using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues
            throw new NotImplementedException("Tax configuration management temporarily disabled");
        }

        public Money CalculateTaxAmount(string customerRegion, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            var totalTax = Money.Zero(Price.Currency);

            // TODO: Implement plan-specific tax configurations using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues

            // Fall back to tax category configurations
            if (TaxCategory != null)
            {
                return TaxCategory.CalculateTotalTax(Price, customerRegion, checkDate);
            }

            return totalTax;
        }

        public Money GetDisplayPrice(string customerRegion, bool includeTax = true, DateTime? date = null)
        {
            if (!includeTax)
                return Price;

            var taxAmount = CalculateTaxAmount(customerRegion, date);
            return Price.Add(taxAmount);
        }

        public Money GetBasePrice(string customerRegion, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;

            // TODO: Implement plan-specific tax configurations using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues

            // Check tax category for inclusive configurations
            if (TaxCategory != null)
            {
                var categoryConfigs = TaxCategory.GetTaxConfigurationsForRegion(customerRegion, checkDate);
                var inclusiveCategoryConfig = categoryConfigs.FirstOrDefault(tc => tc.IsIncluded);

                if (inclusiveCategoryConfig != null)
                {
                    return inclusiveCategoryConfig.CalculateBaseAmountFromTaxInclusivePrice(Price, customerRegion);
                }
            }

            return Price;
        }

        public bool HasTaxConfiguration(TaxType taxType, string region)
        {
            // TODO: Implement using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues
            return false;
        }

        public List<TaxConfiguration> GetActiveTaxConfigurations(string region, DateTime? date = null)
        {
            // TODO: Implement using PlanTaxConfigurations navigation property
            // Temporarily disabled due to EF Core configuration issues
            return new List<TaxConfiguration>();
        }

        public bool IsTaxInclusivePricing(string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;

            // Check tax category configurations only for now
            if (TaxCategory != null)
            {
                var categoryConfigs = TaxCategory.GetTaxConfigurationsForRegion(region, checkDate);
                return categoryConfigs.Any(tc => tc.IsIncluded);
            }

            return false;
        }

        /// <summary>
        /// Creates a new version of this plan with incremented version number
        /// </summary>
        /// <param name="newVersion">The new version number</param>
        /// <returns>A new Plan instance with the same ID and properties but different version</returns>
        public Plan CreateNewVersion(int newVersion)
        {
            if (newVersion <= Version)
                throw new SubscriptionDomainException($"New version {newVersion} must be greater than current version {Version}");

            // Create new plan version with same PlanId but new unique Id
            var newPlan = new Plan(
                name: Name,
                description: Description,
                version: newVersion,
                typeId: TypeId,
                userType: UserType,
                price: Price,
                billingCycle: BillingCycle,
                limits: Limits,
                isPublic: IsPublic,
                trialPeriodDays: TrialPeriodDays,
                setupFee: SetupFeeAmount.HasValue ? Money.Create(SetupFeeAmount.Value / 100m, SetupFeeCurrency!) : null,
                planId: this.PlanId  // CRITICAL: Use same logical PlanId for versioning
            );

            // Note: newPlan.Id will be unique (generated by BaseEntity constructor)
            // but newPlan.PlanId will be the same as this.PlanId for logical grouping

            // Preserve creation timestamp from original plan
            newPlan.CreatedAt = this.CreatedAt;
            newPlan.UpdatedAt = DateTime.UtcNow;

            // Copy all features to the new version
            foreach (var feature in _features)
            {
                newPlan.AddFeature(
                    featureId: feature.FeatureId,
                    name: feature.Name,
                    key: feature.Key,
                    description: feature.Description,
                    isEnabled: feature.IsEnabled
                );
            }

            return newPlan;
        }

        /// <summary>
        /// Gets the next version number for this plan
        /// </summary>
        /// <returns>The next version number</returns>
        public int GetNextVersion()
        {
            return Version + 1;
        }


    }
}
