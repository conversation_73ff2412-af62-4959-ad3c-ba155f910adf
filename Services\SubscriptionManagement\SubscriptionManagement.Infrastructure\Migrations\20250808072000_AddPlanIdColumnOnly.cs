using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddPlanIdColumnOnly : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Add PlanId column as nullable first
            migrationBuilder.AddColumn<Guid>(
                name: "PlanId",
                table: "Plans",
                type: "uuid",
                nullable: true,
                comment: "Logical plan identifier that groups all versions of the same plan");

            // Step 2: Populate PlanId for existing plans
            // For existing plans, set PlanId = Id (each existing plan becomes its own logical plan)
            migrationBuilder.Sql(@"
                UPDATE ""Plans"" 
                SET ""PlanId"" = ""Id"" 
                WHERE ""PlanId"" IS NULL;
            ");

            // Step 3: Make PlanId non-nullable now that all rows have values
            migrationBuilder.AlterColumn<Guid>(
                name: "PlanId",
                table: "Plans",
                type: "uuid",
                nullable: false,
                comment: "Logical plan identifier that groups all versions of the same plan");

            // Step 4: Create indexes
            migrationBuilder.CreateIndex(
                name: "IX_Plans_PlanId",
                table: "Plans",
                column: "PlanId");

            migrationBuilder.CreateIndex(
                name: "UQ_Plans_PlanId_Version",
                table: "Plans",
                columns: new[] { "PlanId", "Version" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Step 1: Drop indexes first
            migrationBuilder.DropIndex(
                name: "UQ_Plans_PlanId_Version",
                table: "Plans");

            migrationBuilder.DropIndex(
                name: "IX_Plans_PlanId",
                table: "Plans");

            // Step 2: Drop the PlanId column
            migrationBuilder.DropColumn(
                name: "PlanId",
                table: "Plans");
        }
    }
}
