using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Services;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class CachedPlanRepository : IPlanRepository
    {
        private readonly IPlanRepository _planRepository;
        private readonly ISubscriptionCacheService _cacheService;
        private readonly ILogger<CachedPlanRepository> _logger;

        public CachedPlanRepository(
            IPlanRepository planRepository,
            ISubscriptionCacheService cacheService,
            ILogger<CachedPlanRepository> logger)
        {
            _planRepository = planRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<Plan?> GetByIdAsync(Guid id)
        {
            try
            {
                // Try cache first
                var cachedPlan = await _cacheService.GetPlanAsync(id);
                if (cachedPlan != null)
                {
                    _logger.LogDebug("Plan {PlanId} retrieved from cache", id);
                    return cachedPlan;
                }

                // Load from database
                var plan = await _planRepository.GetByIdAsync(id);
                if (plan != null)
                {
                    await _cacheService.SetPlanAsync(plan);
                    _logger.LogDebug("Plan {PlanId} loaded from database and cached", id);
                }

                return plan;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting plan {PlanId}", id);
                // Fallback to database only
                return await _planRepository.GetByIdAsync(id);
            }
        }

        public async Task<Plan?> GetByNameAsync(string name)
        {
            // For name-based queries, we'll go directly to the database
            // as caching by name would require additional cache keys
            return await _planRepository.GetByNameAsync(name);
        }

        public async Task<List<Plan>> GetAllAsync()
        {
            // For bulk operations, we'll go directly to the database
            return await _planRepository.GetAllAsync();
        }

        public async Task<List<Plan>> GetActiveAsync()
        {
            return await _planRepository.GetActiveAsync();
        }

        public async Task<List<Plan>> GetPublicAsync()
        {
            return await _planRepository.GetPublicAsync();
        }

        public async Task<List<Plan>> GetByUserTypeAsync(UserType userType)
        {
            return await _planRepository.GetByUserTypeAsync(userType);
        }

        public async Task<List<Plan>> GetByTypeAsync(Guid typeId)
        {
            return await _planRepository.GetByTypeAsync(typeId);
        }

        public async Task<List<Plan>> GetByUserTypeAndTypeAsync(UserType userType, Guid typeId)
        {
            return await _planRepository.GetByUserTypeAndTypeAsync(userType, typeId);
        }

        public async Task<Plan?> GetByBusinessCombinationAsync(UserType userType, Guid typeId, BillingCycle billingCycle)
        {
            // For business combination queries, we'll go directly to the database
            // as this is a validation query and should always be current
            return await _planRepository.GetByBusinessCombinationAsync(userType, typeId, billingCycle);
        }

        public async Task<List<Plan>> GetPlansAsync(int page, int pageSize)
        {
            return await _planRepository.GetPlansAsync(page, pageSize);
        }

        public async Task<int> GetPlansCountAsync()
        {
            return await _planRepository.GetPlansCountAsync();
        }

        public async Task<(List<Plan> Plans, int TotalCount)> GetFilteredPlansAsync(
            int page,
            int pageSize,
            UserType? userType = null,
            bool? isActive = null,
            string? searchTerm = null,
            Guid? planTypeId = null)
        {
            // For filtered queries with complex parameters, we'll go directly to the database
            // as caching these combinations would be complex and may not provide significant benefit
            return await _planRepository.GetFilteredPlansAsync(page, pageSize, userType, isActive, searchTerm, planTypeId);
        }

        public async Task AddAsync(Plan plan)
        {
            await _planRepository.AddAsync(plan);

            // Cache the new plan
            await _cacheService.SetPlanAsync(plan);
            _logger.LogDebug("Added and cached plan {PlanId}", plan.Id);
        }

        public async Task UpdateAsync(Plan plan)
        {
            await _planRepository.UpdateAsync(plan);

            // Update cache
            await _cacheService.SetPlanAsync(plan);
            _logger.LogDebug("Updated and cached plan {PlanId}", plan.Id);
        }

        public async Task DeleteAsync(Plan plan)
        {
            await _planRepository.DeleteAsync(plan);

            // Remove from cache
            await _cacheService.RemovePlanAsync(plan.Id);
            _logger.LogDebug("Deleted plan {PlanId} and removed from cache", plan.Id);
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            // Check cache first
            var cachedPlan = await _cacheService.GetPlanAsync(id);
            if (cachedPlan != null)
            {
                return true;
            }

            return await _planRepository.ExistsAsync(id);
        }

        public async Task<bool> ExistsByNameAsync(string name)
        {
            return await _planRepository.ExistsByNameAsync(name);
        }

        // Usage tracking methods - these should not be cached as they need real-time data
        public async Task<Plan?> GetByIdAndVersionAsync(Guid planId, int version)
        {
            return await _planRepository.GetByIdAndVersionAsync(planId, version);
        }

        public async Task<bool> IsVersionInUseAsync(Guid planId, int version)
        {
            return await _planRepository.IsVersionInUseAsync(planId, version);
        }

        public async Task<int> GetActiveSubscriberCountAsync(Guid planId, int version)
        {
            return await _planRepository.GetActiveSubscriberCountAsync(planId, version);
        }

        public async Task<Dictionary<UserType, int>> GetSubscriberCountByUserTypeAsync(Guid planId, int version)
        {
            return await _planRepository.GetSubscriberCountByUserTypeAsync(planId, version);
        }



        public async Task<int> GetHighestVersionAsync(Guid planId)
        {
            return await _planRepository.GetHighestVersionAsync(planId);
        }

        public async Task<List<Plan>> GetAllVersionsAsync(Guid planId)
        {
            return await _planRepository.GetAllVersionsAsync(planId);
        }

        // New methods for PlanId-based versioning
        public async Task<Plan?> GetByPlanIdAndVersionAsync(Guid planId, int version)
        {
            return await _planRepository.GetByPlanIdAndVersionAsync(planId, version);
        }

        public async Task<bool> IsVersionInUseAsync(Guid planVersionId)
        {
            return await _planRepository.IsVersionInUseAsync(planVersionId);
        }

        public async Task<int> GetActiveSubscriberCountAsync(Guid planVersionId)
        {
            return await _planRepository.GetActiveSubscriberCountAsync(planVersionId);
        }

        public async Task<Dictionary<UserType, int>> GetSubscriberCountByUserTypeAsync(Guid planVersionId)
        {
            return await _planRepository.GetSubscriberCountByUserTypeAsync(planVersionId);
        }

        public async Task<bool> HasActiveSubscriptionsAsync(Guid planId)
        {
            return await _planRepository.HasActiveSubscriptionsAsync(planId);
        }

        public async Task<int> GetNextVersionNumberAsync(Guid planId)
        {
            return await _planRepository.GetNextVersionNumberAsync(planId);
        }
    }
}
