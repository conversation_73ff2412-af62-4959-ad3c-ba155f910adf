using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class NotificationTemplateService : INotificationTemplateService
    {
        private readonly INotificationTemplateRepository _templateRepository;
        private readonly ILogger<NotificationTemplateService> _logger;

        public NotificationTemplateService(
            INotificationTemplateRepository templateRepository,
            ILogger<NotificationTemplateService> logger)
        {
            _templateRepository = templateRepository;
            _logger = logger;
        }

        public async Task<NotificationTemplate?> GetTemplateAsync(
            SubscriptionManagement.Domain.Enums.NotificationType type,
            string channel,
            string language = "en",
            CancellationToken cancellationToken = default)
        {
            return await _templateRepository.GetTemplateAsync(type, channel, language, cancellationToken);
        }

        public async Task<RenderedNotification> RenderTemplateAsync(
            SubscriptionManagement.Domain.Enums.NotificationType type,
            string channel,
            Dictionary<string, string> variables,
            string language = "en",
            CancellationToken cancellationToken = default)
        {
            var template = await GetTemplateAsync(type, channel, language, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"No template found for type {type}, channel {channel}, language {language}");
            }

            if (!template.IsActive)
            {
                throw new InvalidOperationException($"Template for type {type}, channel {channel}, language {language} is not active");
            }

            // Render the template
            var renderedBody = template.Body;
            var renderedSubject = template.Subject;

            foreach (var variable in template.Variables.Keys)
            {
                var placeholder = $"{{{variable}}}";
                var value = variables.ContainsKey(variable) ? variables[variable] : $"[{variable}]";

                renderedBody = renderedBody.Replace(placeholder, value);
                renderedSubject = renderedSubject.Replace(placeholder, value);
            }

            return new RenderedNotification
            {
                Subject = renderedSubject,
                Body = renderedBody,
                Channel = template.Channel,
                Type = template.Type,
                Language = template.Language,
                Metadata = new Dictionary<string, string>(template.Metadata)
            };
        }

        public Dictionary<string, string> GetSubscriptionVariables(
            Subscription subscription,
            Plan? plan = null,
            Payment? payment = null,
            SubscriptionPaymentProof? paymentProof = null)
        {
            var variables = new Dictionary<string, string>
            {
                ["SubscriptionId"] = subscription.Id.ToString(),
                ["UserId"] = subscription.UserId.ToString(),
                ["SubscriptionStatus"] = subscription.Status.ToString(),
                ["StartDate"] = subscription.StartDate.ToString("yyyy-MM-dd"),
                ["NextBillingDate"] = subscription.NextBillingDate.ToString("yyyy-MM-dd"),
                ["CurrentPrice"] = subscription.CurrentPrice.Amount.ToString("F2"),
                ["Currency"] = subscription.CurrentPrice.Currency,
                ["BillingCycle"] = subscription.BillingCycle.ToString(),
                ["AutoRenew"] = subscription.AutoRenew.ToString(),
                ["IsInTrial"] = subscription.IsInTrial().ToString(),
                ["IsExpired"] = subscription.IsExpired().ToString()
            };

            if (subscription.EndDate.HasValue)
                variables["EndDate"] = subscription.EndDate.Value.ToString("yyyy-MM-dd");

            if (subscription.TrialEndDate.HasValue)
                variables["TrialEndDate"] = subscription.TrialEndDate.Value.ToString("yyyy-MM-dd");

            if (subscription.GracePeriodEndDate.HasValue)
                variables["GracePeriodEndDate"] = subscription.GracePeriodEndDate.Value.ToString("yyyy-MM-dd");

            if (plan != null)
            {
                variables["PlanName"] = plan.Name;
                variables["PlanType"] = plan.TypeId.ToString(); // Note: This should be updated to fetch actual type name from TierConfigurationSetup
                variables["PlanDescription"] = plan.Description ?? string.Empty;
            }

            if (payment != null)
            {
                variables["PaymentId"] = payment.Id.ToString();
                variables["PaymentAmount"] = payment.Amount.Amount.ToString("F2");
                variables["PaymentCurrency"] = payment.Amount.Currency;
                variables["PaymentStatus"] = payment.Status.ToString();
                variables["PaymentMethod"] = payment.PaymentMethod ?? string.Empty;

                if (payment.ProcessedAt.HasValue)
                    variables["PaymentProcessedAt"] = payment.ProcessedAt.Value.ToString("yyyy-MM-dd HH:mm");
            }

            if (paymentProof != null)
            {
                variables["PaymentProofId"] = paymentProof.Id.ToString();
                variables["PaymentProofStatus"] = paymentProof.Status.ToString();
                variables["PaymentProofAmount"] = paymentProof.Amount.Amount.ToString("F2");
                variables["PaymentProofCurrency"] = paymentProof.Amount.Currency;
                variables["PaymentDate"] = paymentProof.PaymentDate.ToString("yyyy-MM-dd");

                if (!string.IsNullOrEmpty(paymentProof.RejectionReason))
                    variables["RejectionReason"] = paymentProof.RejectionReason;
            }

            return variables;
        }

        public Dictionary<string, string> GetUserVariables(Guid userId, string? userName = null, string? userEmail = null)
        {
            return new Dictionary<string, string>
            {
                ["UserId"] = userId.ToString(),
                ["UserName"] = userName ?? "[UserName]",
                ["UserEmail"] = userEmail ?? "[UserEmail]",
                ["CurrentDate"] = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                ["CurrentDateTime"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm"),
                ["CompanyName"] = "TLI Logistics",
                ["SupportEmail"] = "<EMAIL>",
                ["SupportPhone"] = "+91-XXXX-XXXX-XX"
            };
        }

        public async Task<List<string>> ValidateTemplateVariablesAsync(
            SubscriptionManagement.Domain.Enums.NotificationType type,
            string channel,
            Dictionary<string, string> variables,
            string language = "en",
            CancellationToken cancellationToken = default)
        {
            var template = await GetTemplateAsync(type, channel, language, cancellationToken);
            if (template == null)
            {
                return new List<string> { $"No template found for type {type}, channel {channel}, language {language}" };
            }

            return template.GetMissingVariables(variables);
        }

        public async Task CreateDefaultTemplatesAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Creating default notification templates");

            var defaultTemplates = GetDefaultTemplates();

            foreach (var template in defaultTemplates)
            {
                var exists = await _templateRepository.ExistsByNameAsync(template.Name, cancellationToken);
                if (!exists)
                {
                    await _templateRepository.AddAsync(template, cancellationToken);
                    _logger.LogInformation("Created default template: {TemplateName}", template.Name);
                }
            }
        }

        private List<NotificationTemplate> GetDefaultTemplates()
        {
            var templates = new List<NotificationTemplate>();

            // Payment Proof Received Template
            templates.Add(NotificationTemplate.Create(
                "PaymentProofReceived_Email_EN",
                "Email notification when payment proof is received",
                SubscriptionManagement.Domain.Enums.NotificationType.PaymentProofReceived,
                "Email",
                "Payment Proof Received - {SubscriptionId}",
                @"Dear {UserName},

We have received your payment proof for subscription {SubscriptionId}.

Payment Details:
- Amount: {PaymentProofAmount} {PaymentProofCurrency}
- Payment Date: {PaymentDate}
- Subscription: {PlanName}

Your payment proof is currently under review. We will notify you once it has been verified.

Thank you for your business!

Best regards,
{CompanyName} Team",
                "en",
                new Dictionary<string, string>
                {
                    ["UserName"] = "User's display name",
                    ["SubscriptionId"] = "Subscription identifier",
                    ["PaymentProofAmount"] = "Payment amount",
                    ["PaymentProofCurrency"] = "Payment currency",
                    ["PaymentDate"] = "Date of payment",
                    ["PlanName"] = "Subscription plan name",
                    ["CompanyName"] = "Company name"
                }));

            // Payment Proof Verified Template
            templates.Add(NotificationTemplate.Create(
                "PaymentProofVerified_Email_EN",
                "Email notification when payment proof is verified",
                SubscriptionManagement.Domain.Enums.NotificationType.PaymentProofVerified,
                "Email",
                "Payment Verified - Subscription Activated",
                @"Dear {UserName},

Great news! Your payment proof has been verified and your subscription has been activated.

Payment Details:
- Amount: {PaymentProofAmount} {PaymentProofCurrency}
- Payment Date: {PaymentDate}
- Subscription: {PlanName}
- Next Billing Date: {NextBillingDate}

Your subscription is now active and you can enjoy all the features of your {PlanName} plan.

Thank you for choosing {CompanyName}!

Best regards,
{CompanyName} Team",
                "en",
                new Dictionary<string, string>
                {
                    ["UserName"] = "User's display name",
                    ["PaymentProofAmount"] = "Payment amount",
                    ["PaymentProofCurrency"] = "Payment currency",
                    ["PaymentDate"] = "Date of payment",
                    ["PlanName"] = "Subscription plan name",
                    ["NextBillingDate"] = "Next billing date",
                    ["CompanyName"] = "Company name"
                }));

            // Payment Proof Rejected Template
            templates.Add(NotificationTemplate.Create(
                "PaymentProofRejected_Email_EN",
                "Email notification when payment proof is rejected",
                SubscriptionManagement.Domain.Enums.NotificationType.PaymentProofRejected,
                "Email",
                "Payment Proof Rejected - Action Required",
                @"Dear {UserName},

We regret to inform you that your payment proof for subscription {SubscriptionId} has been rejected.

Rejection Reason: {RejectionReason}

Payment Details:
- Amount: {PaymentProofAmount} {PaymentProofCurrency}
- Payment Date: {PaymentDate}
- Subscription: {PlanName}

Please review the rejection reason and submit a new payment proof with the correct information.

If you have any questions, please contact our support team at {SupportEmail}.

Best regards,
{CompanyName} Team",
                "en",
                new Dictionary<string, string>
                {
                    ["UserName"] = "User's display name",
                    ["SubscriptionId"] = "Subscription identifier",
                    ["RejectionReason"] = "Reason for rejection",
                    ["PaymentProofAmount"] = "Payment amount",
                    ["PaymentProofCurrency"] = "Payment currency",
                    ["PaymentDate"] = "Date of payment",
                    ["PlanName"] = "Subscription plan name",
                    ["SupportEmail"] = "Support email address",
                    ["CompanyName"] = "Company name"
                }));

            // Subscription Reminder Template
            templates.Add(NotificationTemplate.Create(
                "SubscriptionReminder_Email_EN",
                "Email reminder for subscription renewal",
                SubscriptionManagement.Domain.Enums.NotificationType.SubscriptionReminder,
                "Email",
                "Subscription Renewal Reminder - {PlanName}",
                @"Dear {UserName},

This is a friendly reminder that your {PlanName} subscription will expire on {NextBillingDate}.

Subscription Details:
- Plan: {PlanName}
- Current Price: {CurrentPrice} {Currency}
- Next Billing Date: {NextBillingDate}

To ensure uninterrupted service, please make sure your payment method is up to date or make a manual payment before the expiry date.

Thank you for being a valued customer!

Best regards,
{CompanyName} Team",
                "en",
                new Dictionary<string, string>
                {
                    ["UserName"] = "User's display name",
                    ["PlanName"] = "Subscription plan name",
                    ["NextBillingDate"] = "Next billing date",
                    ["CurrentPrice"] = "Current subscription price",
                    ["Currency"] = "Currency code",
                    ["CompanyName"] = "Company name"
                }));

            return templates;
        }
    }
}
