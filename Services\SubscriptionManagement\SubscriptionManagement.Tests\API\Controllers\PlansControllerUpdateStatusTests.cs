using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Persistence;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace SubscriptionManagement.Tests.API.Controllers
{
    public class PlansControllerUpdateStatusTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public PlansControllerUpdateStatusTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task UpdatePlanStatus_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var planId = await CreateTestPlan();

            // Act
            var response = await _client.PatchAsync($"/api/Plans/{planId}/status", 
                JsonContent.Create(false));

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(content);
            
            Assert.True(result.GetProperty("success").GetBoolean());
            Assert.Contains("deactivated", result.GetProperty("message").GetString());
            Assert.False(result.GetProperty("isActive").GetBoolean());
        }

        [Fact]
        public async Task UpdatePlanStatus_InvalidPlanId_ReturnsNotFound()
        {
            // Arrange
            var invalidPlanId = Guid.NewGuid();

            // Act
            var response = await _client.PatchAsync($"/api/Plans/{invalidPlanId}/status", 
                JsonContent.Create(true));

            // Assert
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(content);
            
            Assert.Equal("DomainError", result.GetProperty("type").GetString());
            Assert.Contains("not found", result.GetProperty("message").GetString());
        }

        [Fact]
        public async Task UpdatePlanStatus_EmptyGuid_ReturnsBadRequest()
        {
            // Act
            var response = await _client.PatchAsync($"/api/Plans/{Guid.Empty}/status", 
                JsonContent.Create(true));

            // Assert
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        }

        private async Task<Guid> CreateTestPlan()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<SubscriptionDbContext>();

            var plan = new Plan(
                "Test Plan for Status Update",
                "Test Description",
                1,
                PlanType.Basic,
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10),
                true);

            context.Plans.Add(plan);
            await context.SaveChangesAsync();

            return plan.Id;
        }
    }
}
