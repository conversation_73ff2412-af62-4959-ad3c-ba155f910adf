using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.UpdatePlan;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Application.Commands
{
    public class UpdatePlanCommandHandlerTests
    {
        private readonly Mock<IPlanRepository> _mockPlanRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<UpdatePlanCommandHandler>> _mockLogger;
        private readonly Mock<IMessageBroker> _mockMessageBroker;
        private readonly UpdatePlanCommandHandler _handler;

        public UpdatePlanCommandHandlerTests()
        {
            _mockPlanRepository = new Mock<IPlanRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<UpdatePlanCommandHandler>>();
            _mockMessageBroker = new Mock<IMessageBroker>();
            _handler = new UpdatePlanCommandHandler(
                _mockPlanRepository.Object,
                _mockMapper.Object,
                _mockLogger.Object,
                _mockMessageBroker.Object);
        }

        [Fact]
        public async Task Handle_PlanNotFound_ThrowsSubscriptionDomainException()
        {
            // Arrange
            var command = new UpdatePlanCommand(Guid.NewGuid()) { Name = "Updated Plan" };
            _mockPlanRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync((Plan?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));
            
            Assert.Contains("not found", exception.Message);
        }

        [Fact]
        public async Task Handle_UpdateBasicProperties_Success()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId);
            var command = new UpdatePlanCommand(planId)
            {
                Name = "Updated Plan Name",
                Description = "Updated Description",
                Price = 199.99m,
                Currency = "INR"
            };

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.IsVersionInUseAsync(It.IsAny<Guid>(), It.IsAny<int>()))
                .ReturnsAsync(false);
            _mockPlanRepository.Setup(x => x.UpdateAsync(It.IsAny<Plan>()))
                .Returns(Task.CompletedTask);
            _mockMapper.Setup(x => x.Map<PlanDetailDto>(It.IsAny<Plan>()))
                .Returns(new PlanDetailDto());

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Updated Plan Name", plan.Name);
            Assert.Equal("Updated Description", plan.Description);
            Assert.Equal(199.99m, plan.Price.Amount);
            _mockPlanRepository.Verify(x => x.UpdateAsync(plan), Times.Once);
            _mockMessageBroker.Verify(x => x.PublishAsync("plan.updated", It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_PlanInUse_CreatesNewVersion()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId);
            var command = new UpdatePlanCommand(planId)
            {
                Name = "Updated Plan Name",
                Price = 299.99m
            };

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.IsVersionInUseAsync(It.IsAny<Guid>(), It.IsAny<int>()))
                .ReturnsAsync(true);
            _mockPlanRepository.Setup(x => x.GetNextVersionNumberAsync(It.IsAny<Guid>()))
                .ReturnsAsync(2);
            _mockPlanRepository.Setup(x => x.AddAsync(It.IsAny<Plan>()))
                .Returns(Task.CompletedTask);
            _mockMapper.Setup(x => x.Map<PlanDetailDto>(It.IsAny<Plan>()))
                .Returns(new PlanDetailDto());

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            _mockPlanRepository.Verify(x => x.AddAsync(It.IsAny<Plan>()), Times.Once);
            _mockMessageBroker.Verify(x => x.PublishAsync("plan.version.created", It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_UpdateLimits_Success()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId);
            var command = new UpdatePlanCommand(planId)
            {
                Limits = new UpdatePlanLimitsDto
                {
                    RfqLimit = 50,
                    VehicleLimit = 25,
                    IsUnlimited = false
                }
            };

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.IsVersionInUseAsync(It.IsAny<Guid>(), It.IsAny<int>()))
                .ReturnsAsync(false);
            _mockPlanRepository.Setup(x => x.UpdateAsync(It.IsAny<Plan>()))
                .Returns(Task.CompletedTask);
            _mockMapper.Setup(x => x.Map<PlanDetailDto>(It.IsAny<Plan>()))
                .Returns(new PlanDetailDto());

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(50, plan.Limits.RfqLimit);
            Assert.Equal(25, plan.Limits.VehicleLimit);
            _mockPlanRepository.Verify(x => x.UpdateAsync(plan), Times.Once);
        }

        [Fact]
        public async Task Handle_UpdateFeatures_Success()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId);
            var featureId = Guid.NewGuid();
            var command = new UpdatePlanCommand(planId)
            {
                Features = new List<UpdatePlanFeatureDto>
                {
                    new UpdatePlanFeatureDto
                    {
                        Action = FeatureUpdateAction.Add,
                        FeatureId = featureId,
                        Name = "New Feature",
                        Key = "new_feature",
                        Description = "A new feature",
                        IsEnabled = true
                    }
                }
            };

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.IsVersionInUseAsync(It.IsAny<Guid>(), It.IsAny<int>()))
                .ReturnsAsync(false);
            _mockPlanRepository.Setup(x => x.UpdateAsync(It.IsAny<Plan>()))
                .Returns(Task.CompletedTask);
            _mockMapper.Setup(x => x.Map<PlanDetailDto>(It.IsAny<Plan>()))
                .Returns(new PlanDetailDto());

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(plan.HasFeature(featureId));
            _mockPlanRepository.Verify(x => x.UpdateAsync(plan), Times.Once);
        }

        [Fact]
        public async Task Handle_CurrencyChange_ThrowsException()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId);
            var command = new UpdatePlanCommand(planId)
            {
                Price = 199.99m,
                Currency = "USD" // Different from plan's INR currency
            };

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.IsVersionInUseAsync(It.IsAny<Guid>(), It.IsAny<int>()))
                .ReturnsAsync(false);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));
            
            Assert.Contains("Cannot change currency", exception.Message);
        }

        private Plan CreateTestPlan(Guid planId)
        {
            return new Plan(
                "Test Plan",
                "Test Description",
                1,
                PlanType.Basic,
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10),
                true);
        }
    }
}
