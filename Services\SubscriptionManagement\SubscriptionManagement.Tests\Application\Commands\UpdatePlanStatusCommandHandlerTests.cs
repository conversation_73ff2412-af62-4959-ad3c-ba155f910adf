using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.UpdatePlanStatus;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Application.Commands
{
    public class UpdatePlanStatusCommandHandlerTests
    {
        private readonly Mock<IPlanRepository> _mockPlanRepository;
        private readonly Mock<ILogger<UpdatePlanStatusCommandHandler>> _mockLogger;
        private readonly Mock<IMessageBroker> _mockMessageBroker;
        private readonly UpdatePlanStatusCommandHandler _handler;

        public UpdatePlanStatusCommandHandlerTests()
        {
            _mockPlanRepository = new Mock<IPlanRepository>();
            _mockLogger = new Mock<ILogger<UpdatePlanStatusCommandHandler>>();
            _mockMessageBroker = new Mock<IMessageBroker>();
            _handler = new UpdatePlanStatusCommandHandler(
                _mockPlanRepository.Object,
                _mockLogger.Object,
                _mockMessageBroker.Object);
        }

        [Fact]
        public async Task Handle_PlanNotFound_ThrowsSubscriptionDomainException()
        {
            // Arrange
            var command = new UpdatePlanStatusCommand(Guid.NewGuid(), true);
            _mockPlanRepository.Setup(x => x.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync((Plan?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));
            
            Assert.Contains("not found", exception.Message);
        }

        [Fact]
        public async Task Handle_ActivatePlan_Success()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId, isActive: false);
            var command = new UpdatePlanStatusCommand(planId, true);

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.UpdateAsync(It.IsAny<Plan>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.True(plan.IsActive);
            _mockPlanRepository.Verify(x => x.UpdateAsync(plan), Times.Once);
            _mockMessageBroker.Verify(x => x.PublishAsync(
                "plan.status.updated",
                It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_DeactivatePlanWithActiveSubscriptions_ThrowsException()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId, isActive: true);
            var command = new UpdatePlanStatusCommand(planId, false);

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.HasActiveSubscriptionsAsync(planId))
                .ReturnsAsync(true);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));
            
            Assert.Contains("active subscriptions", exception.Message);
        }

        [Fact]
        public async Task Handle_DeactivatePlanWithoutActiveSubscriptions_Success()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId, isActive: true);
            var command = new UpdatePlanStatusCommand(planId, false);

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);
            _mockPlanRepository.Setup(x => x.HasActiveSubscriptionsAsync(planId))
                .ReturnsAsync(false);
            _mockPlanRepository.Setup(x => x.UpdateAsync(It.IsAny<Plan>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.False(plan.IsActive);
            _mockPlanRepository.Verify(x => x.UpdateAsync(plan), Times.Once);
        }

        [Fact]
        public async Task Handle_NoStatusChange_ReturnsTrue()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var plan = CreateTestPlan(planId, isActive: true);
            var command = new UpdatePlanStatusCommand(planId, true); // Same status

            _mockPlanRepository.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result);
            _mockPlanRepository.Verify(x => x.UpdateAsync(It.IsAny<Plan>()), Times.Never);
            _mockMessageBroker.Verify(x => x.PublishAsync(
                It.IsAny<string>(),
                It.IsAny<object>()), Times.Never);
        }

        private Plan CreateTestPlan(Guid planId, bool isActive = true)
        {
            var plan = new Plan(
                "Test Plan",
                "Test Description",
                1,
                PlanType.Basic,
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10),
                true);

            if (!isActive)
            {
                plan.Deactivate();
            }

            return plan;
        }
    }
}
