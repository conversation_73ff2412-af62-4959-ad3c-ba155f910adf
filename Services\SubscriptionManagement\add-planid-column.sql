-- SQL script to manually add PlanId column to Plans table
-- This script should be run directly on the PostgreSQL database

-- Step 1: Add PlanId column as nullable first
ALTER TABLE subscription."Plans" 
ADD COLUMN "PlanId" uuid;

-- Step 2: Add comment to the column
COMMENT ON COLUMN subscription."Plans"."PlanId" IS 'Logical plan identifier that groups all versions of the same plan';

-- Step 3: Populate PlanId for existing plans
-- For existing plans, set PlanId = Id (each existing plan becomes its own logical plan)
UPDATE subscription."Plans" 
SET "PlanId" = "Id" 
WHERE "PlanId" IS NULL;

-- Step 4: Make PlanId non-nullable now that all rows have values
ALTER TABLE subscription."Plans" 
ALTER COLUMN "PlanId" SET NOT NULL;

-- Step 5: Create indexes for performance
CREATE INDEX "IX_Plans_PlanId" ON subscription."Plans" ("PlanId");

-- Step 6: Create unique constraint on PlanId + Version combination
CREATE UNIQUE INDEX "UQ_Plans_PlanId_Version" ON subscription."Plans" ("PlanId", "Version");

-- Step 7: Add migration record to history table
INSERT INTO subscription."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250808072000_AddPlanIdColumnOnly', '8.0.11');

-- Verify the changes
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'subscription' 
  AND table_name = 'Plans'
  AND column_name = 'PlanId';

-- Show sample data
SELECT 
    "Id",
    "PlanId",
    "Name",
    "Version",
    "Type",
    "UserType"
FROM subscription."Plans"
LIMIT 5;
