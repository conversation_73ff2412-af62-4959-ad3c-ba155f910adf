# PowerShell script to run the PlanId column migration
# This script connects to PostgreSQL and executes the SQL migration

$connectionString = "Host=**************;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=************"
$sqlFile = "add-planid-column.sql"

Write-Host "🚀 Starting PlanId column migration..." -ForegroundColor Green

try {
    # Check if psql is available
    $psqlPath = Get-Command psql -ErrorAction SilentlyContinue
    if (-not $psqlPath) {
        Write-Host "❌ psql command not found. Please install PostgreSQL client tools." -ForegroundColor Red
        Write-Host "Alternative: Run the SQL script manually in your PostgreSQL client." -ForegroundColor Yellow
        Write-Host "SQL file location: $sqlFile" -ForegroundColor Yellow
        exit 1
    }

    # Execute the SQL script
    Write-Host "📝 Executing SQL migration script..." -ForegroundColor Blue
    $env:PGPASSWORD = "************"
    
    psql -h ************** -p 5432 -U postgres -d TLI_SubscriptionManagement -f $sqlFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
        Write-Host "🎉 PlanId column has been added to the Plans table." -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Test the API to ensure it works with the new PlanId column" -ForegroundColor White
        Write-Host "2. Test the plan versioning functionality" -ForegroundColor White
    } else {
        Write-Host "❌ Migration failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "Please check the error messages above and run the SQL script manually if needed." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Error executing migration: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run the SQL script manually in your PostgreSQL client." -ForegroundColor Yellow
    Write-Host "SQL file location: $sqlFile" -ForegroundColor Yellow
}
finally {
    # Clear the password environment variable
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "📋 Manual execution instructions:" -ForegroundColor Cyan
Write-Host "If the automatic execution failed, you can run the SQL script manually:" -ForegroundColor White
Write-Host "1. Open your PostgreSQL client (pgAdmin, DBeaver, etc.)" -ForegroundColor White
Write-Host "2. Connect to: Host=**************, Port=5432, Database=TLI_SubscriptionManagement" -ForegroundColor White
Write-Host "3. Execute the contents of: $sqlFile" -ForegroundColor White
