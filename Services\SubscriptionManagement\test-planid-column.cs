// Test script to verify PlanId column exists and works correctly
// This can be run as a simple console application or test

using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Infrastructure.Data;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

public class PlanIdColumnTest
{
    public static async Task TestPlanIdColumn()
    {
        // Connection string - update this to match your database
        var connectionString = "Host=**************;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=************";
        
        var options = new DbContextOptionsBuilder<SubscriptionDbContext>()
            .UseNpgsql(connectionString)
            .Options;

        using var context = new SubscriptionDbContext(options);

        try
        {
            Console.WriteLine("=== Testing PlanId Column ===");
            
            // Test 1: Check if we can query Plans with PlanId
            Console.WriteLine("\n1. Testing PlanId column access...");
            var plansWithPlanId = await context.Plans
                .Select(p => new { p.Id, p.PlanId, p.Name, p.Version })
                .Take(5)
                .ToListAsync();
            
            Console.WriteLine($"Found {plansWithPlanId.Count} plans:");
            foreach (var plan in plansWithPlanId)
            {
                Console.WriteLine($"  - Id: {plan.Id}, PlanId: {plan.PlanId}, Name: {plan.Name}, Version: {plan.Version}");
            }

            // Test 2: Test creating a new plan with PlanId
            Console.WriteLine("\n2. Testing plan creation with PlanId...");
            var testPlanId = Guid.NewGuid();
            var testPlan = new Plan(
                name: "Test Plan for PlanId",
                description: "Testing PlanId functionality",
                version: 1,
                type: PlanType.Standard,
                userType: UserType.TransportCompany,
                price: Money.Create(100, "USD"),
                billingCycle: BillingCycle.Monthly,
                limits: new PlanLimits
                {
                    RFQPostingLimit = 10,
                    QuoteSubmissionLimit = 20,
                    QuoteForwardSubmissionLimit = 15,
                    VehicleLimit = 5,
                    UserLimit = 3,
                    SubUserLimit = 2
                },
                planId: testPlanId
            );

            context.Plans.Add(testPlan);
            await context.SaveChangesAsync();
            Console.WriteLine($"✅ Successfully created plan with PlanId: {testPlanId}");

            // Test 3: Test creating a second version with same PlanId
            Console.WriteLine("\n3. Testing plan versioning with same PlanId...");
            var testPlanV2 = new Plan(
                name: "Test Plan for PlanId",
                description: "Testing PlanId functionality - Version 2",
                version: 2,
                type: PlanType.Standard,
                userType: UserType.TransportCompany,
                price: Money.Create(120, "USD"),
                billingCycle: BillingCycle.Monthly,
                limits: new PlanLimits
                {
                    RFQPostingLimit = 15,
                    QuoteSubmissionLimit = 25,
                    QuoteForwardSubmissionLimit = 20,
                    VehicleLimit = 8,
                    UserLimit = 5,
                    SubUserLimit = 3
                },
                planId: testPlanId // Same PlanId, different version
            );

            context.Plans.Add(testPlanV2);
            await context.SaveChangesAsync();
            Console.WriteLine($"✅ Successfully created plan version 2 with same PlanId: {testPlanId}");

            // Test 4: Query all versions of the logical plan
            Console.WriteLine("\n4. Testing querying all versions by PlanId...");
            var allVersions = await context.Plans
                .Where(p => p.PlanId == testPlanId)
                .OrderBy(p => p.Version)
                .Select(p => new { p.Id, p.PlanId, p.Version, p.Price.Amount })
                .ToListAsync();

            Console.WriteLine($"Found {allVersions.Count} versions for PlanId {testPlanId}:");
            foreach (var version in allVersions)
            {
                Console.WriteLine($"  - Version {version.Version}: Id={version.Id}, Price=${version.Amount}");
            }

            // Test 5: Test unique constraint (PlanId + Version)
            Console.WriteLine("\n5. Testing unique constraint on PlanId + Version...");
            try
            {
                var duplicateVersion = new Plan(
                    name: "Duplicate Version Test",
                    description: "This should fail",
                    version: 2, // Same version as testPlanV2
                    type: PlanType.Standard,
                    userType: UserType.TransportCompany,
                    price: Money.Create(150, "USD"),
                    billingCycle: BillingCycle.Monthly,
                    limits: new PlanLimits
                    {
                        RFQPostingLimit = 10,
                        QuoteSubmissionLimit = 20,
                        QuoteForwardSubmissionLimit = 15,
                        VehicleLimit = 5,
                        UserLimit = 3,
                        SubUserLimit = 2
                    },
                    planId: testPlanId // Same PlanId and Version as testPlanV2
                );

                context.Plans.Add(duplicateVersion);
                await context.SaveChangesAsync();
                Console.WriteLine("❌ ERROR: Duplicate PlanId + Version was allowed (constraint not working)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✅ Unique constraint working correctly: {ex.Message}");
            }

            // Cleanup: Remove test plans
            Console.WriteLine("\n6. Cleaning up test data...");
            var testPlans = await context.Plans
                .Where(p => p.PlanId == testPlanId)
                .ToListAsync();
            
            context.Plans.RemoveRange(testPlans);
            await context.SaveChangesAsync();
            Console.WriteLine($"✅ Cleaned up {testPlans.Count} test plans");

            Console.WriteLine("\n=== All tests completed successfully! ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ ERROR: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }
}
