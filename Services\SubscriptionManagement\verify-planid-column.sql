-- SQL script to verify PlanId column in Plans table
-- Run this script in your PostgreSQL database to check the column structure

-- 1. Check if PlanId column exists in Plans table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'subscription' 
  AND table_name = 'Plans' 
  AND column_name = 'PlanId';

-- 2. Show all columns in Plans table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'subscription' 
  AND table_name = 'Plans'
ORDER BY ordinal_position;

-- 3. Check indexes on Plans table
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'subscription' 
  AND tablename = 'Plans';

-- 4. Check constraints on Plans table
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = (
    SELECT oid 
    FROM pg_class 
    WHERE relname = 'Plans' 
      AND relnamespace = (
          SELECT oid 
          FROM pg_namespace 
          WHERE nspname = 'subscription'
      )
);

-- 5. Sample data from Plans table (first 5 rows)
SELECT 
    "Id",
    "PlanId",
    "Name",
    "Version",
    "Type",
    "UserType"
FROM subscription."Plans"
LIMIT 5;

-- 6. Check migration history
SELECT 
    "MigrationId",
    "ProductVersion"
FROM subscription."__EFMigrationsHistory"
ORDER BY "MigrationId";
